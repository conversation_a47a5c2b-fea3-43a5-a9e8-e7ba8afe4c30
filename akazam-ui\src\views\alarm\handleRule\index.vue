<template>
  <div class="app-container">
    <div class="pageName">云管告警处置配置</div>
    <el-row class="formBoxMain">
      <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true" class="flex-box-between"
        label-position="left" label-width="100px">
        <el-col :span="21" :xl="21" :md="19" class="formLeft">
          <el-form-item label="告警名称" prop="alarmNames">
            <el-input v-model="queryParams.alarmNames" class="inputCommon" placeholder="请输入告警名称" clearable
              @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="告警等级" prop="alarmLevels">
            <el-select v-model="queryParams.alarmLevels" placeholder="请选择告警等级" class="inputCommon" clearable>
              <el-option v-for="dict in alarm_severity" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" class="inputCommon" clearable>
              <el-option v-for="dict in sys_job_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="处置建议" prop="handleSuggest">
            <el-select v-model="queryParams.handleSuggest" placeholder="请选择处置建议" class="inputCommon" clearable>
              <el-option v-for="dict in alarm_handle_suggest" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3" :xl="3" :md="5" class="flex-box-end">
          <el-form-item class="displayBlockButtonBox">
            <el-button type="primary" class="displayBlockButton" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button type="info" plain icon="Refresh" class="displayBlockButton" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>

    <el-row :gutter="10" class="mb20">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['alarm:handleRule:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['alarm:handleRule:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="deletes" icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['alarm:handleRule:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Download" @click="handleExport" v-hasPermi="['alarm:handleRule:export']">导出
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="handleRuleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column sortable label="id" align="center" prop="id" />
      <el-table-column sortable label="告警名称" align="center" prop="alarmNames" width="600" />
      <el-table-column sortable label="告警等级" align="center" prop="alarmLevels">
        <template #default="scope">
          <dict-tag :options="alarm_severity" :value="scope.row.alarmLevels" />
        </template>
      </el-table-column>
      <el-table-column sortable label="处置建议" align="center" prop="handleSuggest">
        <template #default="scope">
          <dict-tag :options="alarm_handle_suggest" :value="scope.row.handleSuggest" />
        </template>
      </el-table-column>
      <el-table-column sortable label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_job_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column sortable label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button type="primary" circle icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['alarm:handleRule:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button type="deletes" circle icon="Delete" @click="handleDelete(scope.row)"
              v-hasPermi="['alarm:handleRule:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改云管告警处置配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="handleRuleRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="告警名称" prop="alarmNames">
          <el-input v-model="form.alarmNames" placeholder="请输入告警名称" />
        </el-form-item>
        <el-form-item label="告警等级" prop="alarmLevels">
          <el-select v-model="form.alarmLevels" placeholder="请选择告警等级" multiple clearable style="width: 100% !important;">
            <el-option v-for="dict in alarm_severity" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="dify工作流地址" prop="difyUrl">
          <el-input v-model="form.difyUrl" placeholder="请输入dify工作流地址" />
        </el-form-item>
        <el-form-item label="dify密钥" prop="difyApiKey">
          <el-input v-model="form.difyApiKey" placeholder="请输入dify密钥" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_job_status" :key="dict.value" :label="dict.value">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处置建议" prop="handleSuggest">
          <el-radio-group v-model="form.handleSuggest">
            <el-radio v-for="dict in alarm_handle_suggest" :key="dict.value" :label="dict.value">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HandleRule">
import { addHandleRule, delHandleRule, getHandleRule, listHandleRule, updateHandleRule } from "@/api/alarm/handleRule";

const { proxy } = getCurrentInstance();
const { alarm_severity, sys_job_status, sys_yes_no, alarm_handle_suggest } = proxy.useDict('alarm_severity', 'sys_job_status', 'sys_yes_no', 'alarm_handle_suggest');

const handleRuleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    alarmNames: null,
    handleSuggest: null,
    alarmLevels: null,
    faultOrder: null,
    status: null,
  },
  rules: {
    alarmNames: [
      { required: true, message: "告警名称不能为空", trigger: "blur" }
    ],
    difyApiKey: [
      { required: true, message: "dify密钥不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询云管告警处置配置 */
function getList() {
  loading.value = true;
  listHandleRule(queryParams.value).then(response => {
    handleRuleList.value = response.rows;
    handleRuleList.value.forEach(function (item) {
      item.alarmLevels = item.alarmLevels.split(",");
    });
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    alarmNames: null,
    alarmLevels: ['0', '1', '2', '3', '4'],
    faultOrder: 'N',
    difyUrl: null,
    difyApiKey: null,
    handleSuggest: null,
    status: '0',
    remark: null
  };
  proxy.resetForm("handleRuleRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加云管告警处置配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getHandleRule(_id).then(response => {
    form.value = response.data;
    if (form.value.alarmLevels) {
      form.value.alarmLevels = form.value.alarmLevels.split(",");
    }
    open.value = true;
    title.value = "修改云管告警处置配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["handleRuleRef"].validate(valid => {
    if (valid) {
      form.value.alarmLevels = form.value.alarmLevels.join(",");
      if (form.value.id != null) {
        updateHandleRule(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addHandleRule(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除云管告警处置配置编号为"' + _ids + '"的数据项？').then(function () {
    return delHandleRule(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('alarm/handleRule/export', {
    ...queryParams.value
  }, `handleRule_${new Date().getTime()}.xlsx`)
}

getList();
</script>
