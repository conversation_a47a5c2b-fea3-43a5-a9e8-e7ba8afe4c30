<!-- 消息列表组件 -->
<template>
  <div class="message-list" ref="messageListRef">
    <!-- 调试按钮 (可在生产环境移除) -->
    <!-- <div class="debug-controls">
      <button @click="toggleDebugMode" class="debug-button">
        {{ debugMode ? '关闭调试' : '开启调试' }}
      </button>
    </div> -->

    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <span>加载消息中...</span>
    </div>

    <div v-else-if="messages.length === 0" class="empty-container">
      <div class="welcome-content">
        <img src="@/assets/images/dify/GIF01.gif" alt="logo" class="welcome-logo" />
        <h2 class="welcome-title">你好，我是政务云</h2>
        <h3 class="welcome-subtitle">AI运维助手</h3>
        <!-- 当前页面信息 -->
        <div v-if="welcomeData?.pageInfo?.currentPage" class="current-page-info">
          <el-icon class="page-icon">
            <Setting />
          </el-icon>
          <span>当前页面: {{ welcomeData.pageInfo.currentPage }}</span>
        </div>

        <!-- 页面通知 -->
        <div v-if="welcomeData?.pageInfo?.alarmStats" class="page-notification">
          <div class="notification-title">页面通知</div>
          <div class="notification-content">
            <div class="notification-summary">
              最近<span class="highlight-number">10</span>分钟收到<span class="highlight-number">{{ welcomeData.pageInfo.alarmStats.totalCount }}</span>条告警,其中:
            </div>
            <div class="notification-details">
              <div class="notification-item">
                <el-icon class="notification-icon urgent">
                  <WarningFilled />
                </el-icon>
                <span>紧急<span class="highlight-number">{{welcomeData.pageInfo.alarmStats.statistics.find(s => s.label
                  === '紧急')?.value || 0}}</span>条</span>
              </div>
              <div class="notification-item">
                <el-icon class="notification-icon important">
                  <Warning />
                </el-icon>
                <span>重要<span class="highlight-number">{{welcomeData.pageInfo.alarmStats.statistics.find(s => s.label
                  === '重要')?.value || 0}}</span>条</span>
              </div>
              <div class="notification-item">
                <el-icon class="notification-icon minor">
                  <InfoFilled />
                </el-icon>
                <span>次要<span class="highlight-number">{{welcomeData.pageInfo.alarmStats.statistics.find(s => s.label
                  === '次要')?.value || 0}}</span>条</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 建议问题 -->
        <div v-if="mergedExampleQuestions && mergedExampleQuestions.length > 0" class="suggested-questions">
          <div class="suggested-title">您可能想问</div>
          <div class="example-questions">
            <div v-for="(question, qIndex) in mergedExampleQuestions" :key="qIndex" class="question-card"
              @click="emitExampleQuestion(question)">
              <span class="question-number">{{ qIndex + 1 }}</span>
              <span class="question-text">{{ question }}</span>
              <el-icon class="arrow-icon">
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template v-else>
      <!-- 消息列表 -->
      <div v-for="(message, index) in messages" :key="message.id || index" class="message-item" :class="{
        'user-message': message.user_message,
        'assistant-message': !message.user_message,
        'agent-message': message.is_agent,
        'waiting-message': !message.user_message && message.waiting_for_response
      }">
        <!-- 用户消息 -->
        <template v-if="message.user_message">
          <div class="message-content user-content">
            <div class="message-text user-text">{{ message.query }}</div>
            <!-- 文件内容指示器 -->
            <div v-if="message.has_file_content" class="file-content-indicator">
              <div class="indicator-icon">📄</div>
              <div class="indicator-text">包含文件内容</div>
            </div>
          </div>
        </template>

        <!-- 助手消息 -->
        <template v-else>
          <div class="message-avatar assistant-avatar"></div>
          <div class="message-content">
            <!-- 等待响应状态 -->
            <div v-if="message.waiting_for_response" class="waiting-container">
              <div class="typing-bubbles">
                <div class="bubble"></div>
                <div class="bubble"></div>
                <div class="bubble"></div>
              </div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="message.error" class="error-message">
              {{ message.error_message || '响应出错，请重试' }}
            </div>

            <!-- 调试信息 -->
            <div v-else-if="debugMode" class="debug-message">
              <pre>{{ JSON.stringify(message, null, 2) }}</pre>
            </div>

            <!-- 正常消息内容 -->
            <template v-else>
              <!-- 按照流式接收顺序显示内容 -->
              <div v-if="message.streamParts && message.streamParts.length > 0" class="stream-parts-container">
                <template v-for="(part, pIndex) in getOrderedParts(message)" :key="`${message.id}-part-${pIndex}`">
                  <!-- 文本消息部分 -->
                  <span v-if="part.type === 'message' || part.type === 'agent_message'" class="message-part">
                    <span class="message-text-content" v-html="formatMarkdownText(part.content)"></span>
                  </span>

                  <!-- Agent思考部分/工具调用 - 使用新组件 -->
                  <dify-tool-call v-else-if="part.type === 'agent_thought' && part.tool" :tool-data="part"
                    :message-id="message.id" :tool-id="part.id || pIndex" :debug-mode="debugMode"
                    @router-click="handleRouterClick" @change-mode="handleChangeMode" class="agent-thought-part" />

                  <!-- 文件部分 -->
                  <div v-else-if="part.type === 'message_file'" class="file-part">
                    <div class="file-container">
                      <img v-if="part.fileId && getFileById(message, part.fileId)?.type === 'image'"
                        :src="getFileById(message, part.fileId)?.url" alt="图片" class="file-image"
                        @click="handleFilePreview(getFileById(message, part.fileId))" />
                      <div v-else-if="part.fileId && getFileById(message, part.fileId)" class="file-document"
                        @click="handleFilePreview(getFileById(message, part.fileId))">
                        <div class="document-icon"></div>
                        <span>{{ getFileNameFromUrl(getFileById(message, part.fileId)?.url) }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </div>

              <!-- 显示非流式响应内容 -->
              <div v-else-if="message.answer" class="message-text-content plain-message"
                v-html="formatMarkdownText(message.answer)">
              </div>

              <!-- 显示历史消息中的agent_thoughts内容 - 使用新组件 -->
              <div
                v-if="(!message.streamParts || message.streamParts.length === 0) && message.agent_thoughts && message.agent_thoughts.length > 0"
                class="history-agent-thoughts">
                <template v-for="(thought, tIndex) in message.agent_thoughts" :key="`${message.id}-thought-${tIndex}`">
                  <dify-tool-call v-if="thought.tool" :tool-data="thought" :message-id="message.id"
                    :tool-id="thought.id || tIndex" :debug-mode="debugMode" @router-click="handleRouterClick"
                    @change-mode="handleChangeMode" class="agent-thought-part" />
                </template>
              </div>

              <!-- 打字光标 (在流式响应中的最后一条消息显示) -->
              <div
                v-if="streaming && isLastAssistantMessage(index) && !message.waiting_for_response && shouldShowCursor(message)"
                class="typing-cursor"></div>

              <!-- 停止响应提示 -->
              <div v-if="message.response_stopped" class="response-stopped-notice">
                响应已停止
              </div>

              <!-- 引用资源 -->
              <div v-if="message.retriever_resources && message.retriever_resources.length > 0"
                class="retriever-resources">
                <div class="resources-title">参考资料</div>
                <div v-for="(resource, rIndex) in message.retriever_resources" :key="rIndex" class="resource-item">
                  <div class="resource-title">{{ resource.document_name || '未命名文档' }}</div>
                  <div class="resource-content">{{ resource.content }}</div>
                </div>
              </div>

              <!-- 消息中的文件列表 -->
              <div v-if="message.message_files && message.message_files.length > 0" class="message-files">
                <div class="files-title">附件文件</div>
                <div class="files-list">
                  <div v-for="file in message.message_files" :key="file.id" class="file-item"
                    @click="handleFilePreview(file)">
                    <span class="file-icon">
                      <el-icon v-if="getFileType(file) === 'image'">
                        <Picture />
                      </el-icon>
                      <el-icon v-else-if="getFileType(file) === 'docx'">
                        <Document />
                      </el-icon>
                      <el-icon v-else-if="getFileType(file) === 'excel'">
                        <Files />
                      </el-icon>
                      <el-icon v-else-if="getFileType(file) === 'pdf'">
                        <Document />
                      </el-icon>
                      <el-icon v-else>
                        <Files />
                      </el-icon>
                    </span>
                    <span class="file-name">{{ getFileNameFromUrl(file.url) }}</span>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import { ref, watch, nextTick, computed, reactive, onMounted, onUnmounted } from 'vue'
import { ChatLineRound, ArrowRight, Picture, Document, Files, Setting, WarningFilled, Warning, InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ElButton, ElMessage } from 'element-plus'
import DifyToolCall from './DifyToolCall.vue' // 导入新组件

export default {
  name: 'MessageList',
  components: {
    ChatLineRound,
    ArrowRight,
    Picture,
    Document,
    Files,
    Setting,
    WarningFilled,
    Warning,
    InfoFilled,
    ElButton,
    DifyToolCall // 注册新组件
  },
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    streaming: {
      type: Boolean,
      default: false
    },
    waitingResponse: {
      type: Boolean,
      default: false
    },
    streamingMessageId: {
      type: String,
      default: ''
    },
    streamingConversationId: {
      type: String,
      default: ''
    },
    exampleQuestions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['select-example', 'preview-file', 'change-mode'],
  setup(props, { emit }) {
    const messageListRef = ref(null)
    const debugMode = ref(false)  // 关闭调试模式，默认不展开
    const router = useRouter()

    // 使用计算属性安全获取props
    const isWaiting = computed(() => {
      return !!props.waitingResponse
    })

    // 获取欢迎页数据
    const welcomeDataRef = ref(null)

    // 监听全局数据变化
    const updateWelcomeData = () => {
      welcomeDataRef.value = window.__ALARM_WELCOME_DATA__ || null
    }

    // 初始检查
    updateWelcomeData()

    // 监听全局事件来更新数据
    onMounted(() => {
      window.addEventListener('dify-set-example-questions', updateWelcomeData)
      window.addEventListener('dify-welcome-data-updated', updateWelcomeData)
    })

    onUnmounted(() => {
      window.removeEventListener('dify-set-example-questions', updateWelcomeData)
      window.removeEventListener('dify-welcome-data-updated', updateWelcomeData)
    })

    // 监听 exampleQuestions props 变化
    watch(() => props.exampleQuestions, (newQuestions) => {
      console.log('MessageList - exampleQuestions props changed:', newQuestions)
    }, { deep: true })

    const welcomeData = computed(() => {
      console.log('MessageList - welcomeData computed called, value:', welcomeDataRef.value)
      return welcomeDataRef.value
    })

    // 合并建议问题：优先使用props中的exampleQuestions，如果没有则使用welcomeData中的
    const mergedExampleQuestions = computed(() => {

      if (props.exampleQuestions && props.exampleQuestions.length > 0) {
        return props.exampleQuestions
      }
      if (welcomeData.value?.suggestedQuestions && welcomeData.value.suggestedQuestions.length > 0) {
        return welcomeData.value.suggestedQuestions
      }
      // 默认建议问题
      return [
        '待人工审核的告警有哪些？',
        '信创资源池的告警有哪些？',
        '最近10分钟的重要告警？'
      ]
    })

    // 计算通知等级
    const notificationLevel = computed(() => {
      if (!welcomeData.value?.pageInfo?.alarmStats?.statistics) {
        return null
      }

      const stats = welcomeData.value.pageInfo.alarmStats.statistics
      const urgentCount = stats.find(s => s.label === '紧急')?.value || 0
      const importantCount = stats.find(s => s.label === '重要')?.value || 0
      const minorCount = stats.find(s => s.label === '次要')?.value || 0

      if (urgentCount > 0) {
        return {
          level: 'urgent',
          text: '紧急',
          color: '#ff4757',
          icon: 'WarningFilled'
        }
      } else if (importantCount > 0) {
        return {
          level: 'important',
          text: '重要',
          color: '#ffa502',
          icon: 'Warning'
        }
      } else if (minorCount > 0) {
        return {
          level: 'minor',
          text: '次要',
          color: '#3742fa',
          icon: 'InfoFilled'
        }
      }

      return {
        level: 'normal',
        text: '正常',
        color: '#2ed573',
        icon: 'InfoFilled'
      }
    })

    // 工具展开/折叠状态管理
    const expandedTools = reactive({})

    // 处理示例问题点击
    const emitExampleQuestion = (question) => {
      // 检查是否有自定义点击事件
      const customClickEvent = window.__DIFY_CUSTOM_CLICK_EVENT__

      if (customClickEvent) {
        // 如果有自定义点击事件，触发自定义事件而不是发送给大模型
        window.dispatchEvent(new CustomEvent(customClickEvent, {
          detail: question
        }))
      } else {
        // 没有自定义点击事件，使用默认行为（发送给大模型）
        emit('select-example', question)
      }
    }

    const toggleTool = (messageId, partId) => {
      const key = `${messageId}-${partId}`
      expandedTools[key] = !expandedTools[key]
    }

    const isToolExpanded = (messageId, partId) => {
      const key = `${messageId}-${partId}`
      return expandedTools[key] === true
    }

    // 安全解析JSON
    const safeParseJSON = (text) => {
      try {
        return JSON.parse(text)
      } catch (e) {
        return null
      }
    }

    // 解码Unicode编码
    const decodeUnicodeIfNeeded = (text) => {
      if (!text) return '';

      // 检查是否包含Unicode编码模式 \uXXXX
      if (typeof text === 'string' && /\\u[\da-f]{4}/i.test(text)) {
        try {
          // 尝试解码Unicode
          return JSON.parse(`"${text.replace(/"/g, '\\"')}"`);
        } catch (e) {
          console.error('Unicode解码失败:', e);
          return text;
        }
      }

      return text;
    }

    // 格式化Markdown文本
    const formatMarkdownText = (text) => {
      if (!text) return '';

      // 处理代码块
      let processedText = text.replace(/```(.*?)```/gs, (match, code) => {
        return `<pre class="markdown-code-block">${code}</pre>`;
      });

      // 处理表格 - 转换Markdown表格为HTML表格
      processedText = processedText.replace(/(\|[^\n]+\|\n)(\|[-|\s]+\|\n)(([\s\S]*?)(?=\n\n|\n$|$))/g, (match, header, separator, rows) => {
        // 提取表头
        const headerCols = header.split('|').map(col => col.trim()).filter(col => col);

        // 提取行
        const tableRows = rows.split('\n').filter(row => row.includes('|'));

        // 构建HTML表格
        let table = '<div class="markdown-table"><table>';

        // 添加表头
        table += '<thead><tr>';
        headerCols.forEach(col => {
          table += `<th>${col}</th>`;
        });
        table += '</tr></thead>';

        // 添加表体
        table += '<tbody>';
        tableRows.forEach(row => {
          if (!row.trim()) return;

          table += '<tr>';
          row.split('|').map(col => col.trim()).filter(col => col !== '').forEach(col => {
            table += `<td>${col}</td>`;
          });
          table += '</tr>';
        });

        table += '</tbody></table></div>';
        return table;
      });

      // 如果没有成功处理复杂表格，回退到简单处理
      if (!processedText.includes('<table>')) {
        processedText = text.replace(/\|(.*?)\|/g, (match) => {
          return `<div class="markdown-table-row">${match}</div>`;
        });
      }

      // 处理加粗文本
      processedText = processedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

      // 处理斜体文本
      processedText = processedText.replace(/\*(.*?)\*/g, '<em>$1</em>');

      // 转换换行符为HTML换行
      processedText = processedText.replace(/\n/g, '<br>');

      return processedText;
    }

    // 递归查找JSON对象中的result字段
    const findResultField = (obj) => {
      // 如果是数组，检查每个元素
      if (Array.isArray(obj)) {
        for (const item of obj) {
          const result = findResultField(item)
          if (result) return result
        }
        return null
      }

      // 如果不是对象，返回null
      if (!obj || typeof obj !== 'object') {
        return null
      }

      // 如果直接有result字段，且包含router属性，返回结果
      if (obj.result && typeof obj.result === 'object' && obj.result.router) {
        return obj.result
      }

      // 递归检查所有属性
      for (const key in obj) {
        // 跳过非对象类型的属性
        if (!obj[key] || typeof obj[key] !== 'object') {
          continue
        }

        const result = findResultField(obj[key])
        if (result) return result
      }

      return null
    }

    // 判断是否包含特殊数据类型
    const hasSpecialDataType = (part) => {
      if (!part) return false

      const observation = part.observation || getObservationFromContent(part)
      if (!observation) return false

      try {
        // 提取可能包含的数据类型信息
        const routerInfo = extractRouterInfo(observation)
        if (!routerInfo) return false

        // 检查是否包含router或chart类型
        if (routerInfo.dataType === 'router' || routerInfo.dataType === 'chart' ||
          routerInfo.router || routerInfo.chartType ||
          (routerInfo.result && (routerInfo.result.dataType === 'router' || routerInfo.result.dataType === 'chart'))) {
          return true
        }
        return false
      } catch (e) {
        console.error('检查特殊数据类型失败:', e)
        return false
      }
    }

    // 获取数据类型
    const getDataType = (part) => {
      if (!part) return null

      const observation = part.observation || getObservationFromContent(part)
      if (!observation) return null

      try {
        // 提取可能包含的数据类型信息
        const routerInfo = extractRouterInfo(observation)
        if (!routerInfo) return null

        // 直接检查dataType字段
        if (routerInfo.dataType) {
          return routerInfo.dataType
        }

        // 检查result中的dataType
        if (routerInfo.result && routerInfo.result.dataType) {
          return routerInfo.result.dataType
        }

        // 通过特征判断类型
        if (routerInfo.router || routerInfo.result?.router) {
          return 'router'
        }

        if (routerInfo.chartType || routerInfo.result?.chartType ||
          (routerInfo.result?.dataX && routerInfo.result?.dataY)) {
          return 'chart'
        }

        return null
      } catch (e) {
        console.error('获取数据类型失败:', e)
        return null
      }
    }

    // 从结构化内容中提取工具输入
    const getToolInputFromContent = (part) => {
      if (!part || !part.content) return null

      try {
        // 尝试解析JSON
        const data = safeParseJSON(part.content)
        if (data && data.tool_input) {
          return data.tool_input
        }

        // 尝试从content字符串中直接提取tool_input字段
        const toolInputMatch = part.content.match(/"tool_input"\s*:\s*"([^"]+)"/)
        if (toolInputMatch && toolInputMatch[1]) {
          return toolInputMatch[1]
        }

        // 尝试从content字符串中提取带引号的tool_input字段
        const toolInputMatch2 = part.content.match(/"tool_input"\s*:\s*"([^"]*)"/)
        if (toolInputMatch2) {
          return toolInputMatch2[1]
        }

        // 尝试从content字符串中提取不带引号的tool_input字段(数字或布尔值)
        const toolInputMatch3 = part.content.match(/"tool_input"\s*:\s*([^,}\s]+)/)
        if (toolInputMatch3) {
          return toolInputMatch3[1]
        }

        return null
      } catch (e) {
        console.error('获取工具输入失败:', e)
        return null
      }
    }

    // 从结构化内容中提取工具观察结果
    const getObservationFromContent = (part) => {
      if (!part || !part.content) return null

      try {
        // 尝试解析JSON
        const data = safeParseJSON(part.content)
        if (data && data.observation) {
          return data.observation
        }

        // 尝试从content字符串中直接提取observation字段 - 改进正则，支持更多字符
        const observationMatch = part.content.match(/"observation"\s*:\s*"([^"]*)"/)
        if (observationMatch && observationMatch[1]) {
          return observationMatch[1]
        }

        // 尝试从content字符串中提取带引号的多行observation字段 - 改进正则
        const observationRegex = /"observation"\s*:\s*"([\s\S]*?)(?:(?<!\\)"|$)/
        const observationMatch2 = part.content.match(observationRegex)
        if (observationMatch2 && observationMatch2[1]) {
          // 去除转义字符
          return observationMatch2[1].replace(/\\"/g, '"').replace(/\\\\/g, '\\')
        }

        // 尝试提取不带引号的observation字段(可能是嵌套对象)
        const obsStartIndex = part.content.indexOf('"observation":')
        if (obsStartIndex !== -1) {
          // 找到observation字段开始位置之后的部分
          const obsContentStart = part.content.indexOf(':', obsStartIndex) + 1
          let obsContent = part.content.substring(obsContentStart).trim()

          // 如果以{开头，可能是嵌套对象
          if (obsContent.startsWith('{')) {
            let braceCount = 1
            let endIndex = 1

            // 寻找匹配的右花括号
            while (endIndex < obsContent.length && braceCount > 0) {
              const char = obsContent[endIndex]
              if (char === '{') braceCount++
              else if (char === '}') braceCount--
              endIndex++
            }

            if (braceCount === 0) {
              // 提取嵌套对象并尝试解析
              const nestedObj = obsContent.substring(0, endIndex)
              try {
                // 返回字符串格式的JSON对象
                return nestedObj
              } catch (e) {
                console.error('解析嵌套observation对象失败:', e)
              }
            }
          }
        }

        return null
      } catch (e) {
        console.error('获取工具观察结果失败:', e)
        return null
      }
    }

    // 检查是否有观察结果
    const hasObservation = (part) => {
      if (!part) return false
      return !!part.observation || !!getObservationFromContent(part)
    }

    // 获取工具状态
    const getToolStatus = (part) => {
      if (hasObservation(part)) {
        return 'status-completed'
      }
      return 'status-running'
    }

    // 处理可能包含转义JSON字符串的数据
    const extractNestedJSON = (text) => {
      if (!text) return null

      try {
        // 尝试直接解析
        const directParse = safeParseJSON(text)
        if (directParse) return directParse

        // 寻找JSON字符串模式
        const jsonPattern = /\{.*\}/s
        const match = text.match(jsonPattern)
        if (!match) return null

        // 尝试解析匹配到的部分
        const extracted = match[0]
        const parsed = safeParseJSON(extracted)
        if (parsed) return parsed

        // 处理双重转义的情况
        const unescaped = extracted.replace(/\\"/g, '"').replace(/\\\\/g, '\\')
        return safeParseJSON(unescaped)
      } catch (e) {
        console.error('提取嵌套JSON失败:', e)
        return null
      }
    }

    // 从观察结果中提取router信息
    const extractRouterInfo = (observation) => {
      if (!observation) return null

      try {
        // 深层解析和查找result字段的多层策略

        // 1. 直接从字符串中提取router字段 (最直接的方式)
        const routerRegex = /"router"\s*:\s*"([^"]+)"/
        const routerMatch = observation.match(routerRegex)
        if (routerMatch && routerMatch[1]) {
          return {
            router: routerMatch[1],
            title: extractTitle(observation),
            siteCount: extractSiteCount(observation),
            statisticMsg: extractStatisticMsg(observation)
          }
        }

        // 2. 尝试处理JSON格式 (多层解析策略)
        let jsonData = null

        // 2.1 尝试直接解析
        jsonData = safeParseJSON(observation)
        if (jsonData) {
          const result = findResultField(jsonData)
          if (result) {
            return result
          }
        }

        // 2.2 尝试从各种可能的字段中提取JSON
        const potentialJsonFields = [
          observation,
          jsonData?.businessAccess,
          jsonData?.getSiteDetectLine,
          // 可以添加更多可能的字段
        ]

        for (const field of potentialJsonFields) {
          if (!field) continue

          // 处理字符串字段
          if (typeof field === 'string') {
            const nestedJson = extractNestedJSON(field)
            if (nestedJson) {
              const result = findResultField(nestedJson)
              if (result) {
                return result
              }
            }
          }

          // 处理对象字段
          if (typeof field === 'object') {
            const result = findResultField(field)
            if (result) {
              return result
            }
          }
        }

        // 3. 如果以上都失败，使用正则表达式提取关键字段 (回退策略)
        return {
          router: extractRouter(observation),
          title: extractTitle(observation),
          siteCount: extractSiteCount(observation),
          statisticMsg: extractStatisticMsg(observation)
        }
      } catch (e) {
        console.error('提取router信息失败:', e)
        return null
      }
    }

    // 辅助函数：提取router
    const extractRouter = (text) => {
      const match = text.match(/"router"\s*:\s*"([^"]+)"/)
      return match ? match[1] : null
    }

    // 辅助函数：提取title
    const extractTitle = (text) => {
      // 改进匹配正则表达式，支持更多格式的标题提取
      const patterns = [
        /"title"\s*:\s*"([^"]+)"/,         // 标准JSON格式 "title": "值"
        /'title'\s*:\s*'([^']+)'/,         // 单引号格式 'title': '值'
        /"title"\s*:\s*([^,"}\s]+)/,       // 无引号值 "title": 值
        /title\s*[:=]\s*["']([^"']+)["']/  // 键无引号 title: "值"
      ]

      for (const pattern of patterns) {
        const match = text.match(pattern)
        if (match && match[1]) {
          return match[1]
        }
      }

      return '查看详情'
    }

    // 辅助函数：提取siteCount
    const extractSiteCount = (text) => {
      const match = text.match(/"siteCount"\s*:\s*"([^"]+)"/)
      return match ? match[1] : ''
    }

    // 辅助函数：提取statisticMsg
    const extractStatisticMsg = (text) => {
      const match = text.match(/"statisticMsg"\s*:\s*"([^"]+)"/)
      return match ? match[1] : ''
    }

    // 从观察结果中提取JSON部分 - 使用递归查找替代固定字段
    const extractJsonFromObservation = (observation) => {
      return extractRouterInfo(observation)
    }

    // 处理路由点击
    const handleRouterClick = (routerPath) => {
      if (!routerPath) return;

      try {
        // 尝试路由导航
        router.push(routerPath);
      } catch (e) {
        console.error('路由导航失败:', e);
        ElMessage.error('页面导航失败，请检查路由路径');
      }
    };

    // 获取路由标题
    const getRouterTitle = (part) => {
      const observation = part.observation || getObservationFromContent(part)
      if (!observation) return '查看详情'

      try {
        // 先尝试从JSON中提取标题
        const jsonData = safeParseJSON(observation)
        if (jsonData) {
          // 递归查找title字段
          const findTitle = (obj) => {
            if (!obj || typeof obj !== 'object') return null

            // 直接查找title字段
            if (obj.title) return obj.title

            // 查找result.title
            if (obj.result && obj.result.title) return obj.result.title

            // 递归查找所有对象属性
            for (const key in obj) {
              if (typeof obj[key] === 'object') {
                const title = findTitle(obj[key])
                if (title) return title
              }
            }
            return null
          }

          const title = findTitle(jsonData)
          if (title) return title
        }

        // 如果JSON提取失败，使用正则表达式提取
        const routerInfo = extractRouterInfo(observation)
        if (routerInfo && routerInfo.title) {
          return routerInfo.title
        }

        // 使用增强的提取标题函数
        return extractTitle(observation) || '查看详情'
      } catch (e) {
        console.error('获取路由标题失败:', e)
        return '查看详情'
      }
    }

    // 获取站点数量信息
    const getRouterSiteCount = (part) => {
      const observation = part.observation || getObservationFromContent(part)
      if (!observation) return ''

      try {
        const routerInfo = extractRouterInfo(observation)
        if (routerInfo && routerInfo.siteCount) {
          return routerInfo.siteCount
        }

        return extractSiteCount(observation) || ''
      } catch (e) {
        console.error('获取站点数量失败:', e)
        return ''
      }
    }

    // 获取统计信息
    const getRouterStatisticMsg = (part) => {
      const observation = part.observation || getObservationFromContent(part)
      if (!observation) return ''

      try {
        const routerInfo = extractRouterInfo(observation)
        if (routerInfo && routerInfo.statisticMsg) {
          return routerInfo.statisticMsg
        }

        return extractStatisticMsg(observation) || ''
      } catch (e) {
        console.error('获取统计信息失败:', e)
        return ''
      }
    }

    // 渲染图表
    const renderChart = (messageId, partId, part) => {
      nextTick(() => {
        const observation = part.observation || getObservationFromContent(part)
        if (!observation) return

        try {
          const chartId = `chart-${messageId}-${partId}`
          const chartContainer = document.getElementById(chartId)
          if (!chartContainer) {
            console.warn('未找到图表容器:', chartId)
            return
          }

          // 从观察结果中提取图表数据
          const dataJson = extractJsonFromObservation(observation)
          if (!dataJson) {
            console.warn('未找到有效的图表数据')
            return
          }

          // 递归查找图表数据
          const findChartData = (obj) => {
            if (!obj) return null

            // 直接从顶层查找
            if (obj.chartType || obj.dataX || obj.dataY) {
              return obj
            }

            // 检查result字段
            if (obj.result) {
              if (obj.result.chartType || obj.result.dataX || obj.result.dataY) {
                return obj.result
              }
            }

            // 递归检查所有对象属性
            if (typeof obj === 'object' && !Array.isArray(obj)) {
              for (const key in obj) {
                if (typeof obj[key] === 'object') {
                  const result = findChartData(obj[key])
                  if (result) return result
                }
              }
            }

            return null
          }

          // 查找图表数据
          const chartData = findChartData(dataJson)
          if (!chartData) {
            console.warn('未找到图表数据结构')
            return
          }

          // 导入echarts
          import('echarts').then(echarts => {
            const chart = echarts.init(chartContainer)

            // 获取图表类型和数据
            const chartType = chartData.chartType || 'line'
            let dataX = chartData.dataX || []
            let dataY = chartData.dataY || []
            let chartTitle = chartData.title || '数据可视化'
            let chartSeries = []

            // 处理可能的数组格式 (多系列数据)
            if (Array.isArray(dataY) && dataY.length > 0 && Array.isArray(dataY[0])) {
              // 多系列数据
              chartSeries = dataY.map((series, index) => {
                return {
                  name: chartData.seriesNames?.[index] || `系列${index + 1}`,
                  type: chartType,
                  data: series
                }
              })
            } else {
              // 单系列数据
              chartSeries = [{
                name: chartData.seriesNames?.[0] || '数据',
                type: chartType,
                data: dataY
              }]
            }

            // 饼图特殊处理
            if (chartType === 'pie') {
              const pieData = []
              for (let i = 0; i < dataX.length; i++) {
                pieData.push({
                  name: dataX[i],
                  value: Array.isArray(dataY[0]) ? dataY[0][i] : dataY[i]
                })
              }

              chartSeries = [{
                name: chartData.seriesNames?.[0] || '数据',
                type: 'pie',
                radius: '50%',
                data: pieData
              }]
            }

            // 根据图表类型设置选项
            let option = {}

            if (chartType === 'pie') {
              option = {
                title: {
                  text: chartTitle,
                  left: 'center'
                },
                tooltip: {
                  trigger: 'item',
                  formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                  orient: 'vertical',
                  left: 10,
                  data: dataX
                },
                series: chartSeries
              }
            } else {
              // 折线图和柱状图等直角坐标系图表
              option = {
                title: {
                  text: chartTitle
                },
                tooltip: {
                  trigger: 'axis'
                },
                legend: {
                  data: chartSeries.map(item => item.name)
                },
                xAxis: {
                  type: 'category',
                  data: dataX
                },
                yAxis: {
                  type: 'value'
                },
                series: chartSeries
              }
            }

            // 渲染图表
            chart.setOption(option)

            // 自适应窗口大小
            window.addEventListener('resize', () => {
              chart.resize()
            })

            // 设置点击事件
            chart.on('click', (params) => {
              // 可以在这里处理点击事件
            })
          }).catch(err => {
            console.error('加载echarts失败:', err)
          })
        } catch (e) {
          console.error('渲染图表失败:', e)
        }
      })
    }

    // 监听工具调用展开状态变化，在展开时渲染图表
    watch(expandedTools, (newVal, oldVal) => {
      for (const key in newVal) {
        if (newVal[key] && (!oldVal || !oldVal[key])) {
          // 解析key得到messageId和partId
          const [messageId, partId] = key.split('-')

          // 查找对应的part
          const message = props.messages.find(m => m.id === messageId)
          if (!message || !message.streamParts) continue

          const part = message.streamParts.find(p => (p.id || message.streamParts.indexOf(p)) === partId)
          if (!part) continue

          // 如果是图表类型，渲染图表
          if (getDataType(part) === 'chart') {
            renderChart(messageId, partId, part)
          }
        }
      }
    }, { deep: true })

    // 监听消息变化，自动展开和渲染特殊数据类型工具调用
    watch(() => props.messages, (newMessages) => {
      if (!newMessages) return

      nextTick(() => {
        newMessages.forEach(message => {
          if (!message.streamParts) return

          message.streamParts.forEach((part, index) => {
            if (part.type === 'agent_thought' && hasSpecialDataType(part)) {
              // 自动展开特殊数据类型工具调用
              const key = `${message.id}-${part.id || index}`
              expandedTools[key] = true

              // 如果是图表类型，渲染图表
              if (getDataType(part) === 'chart') {
                renderChart(message.id, part.id || index, part)
              }
            }
          })
        })
      })
    }, { deep: true })

    // 监听消息变化，自动滚动到底部
    watch(
      [() => props.messages, () => props.streaming],
      () => {
        nextTick(() => {
          scrollToBottom()
        })
      },
      { deep: true }
    )

    // 滚动到底部
    const scrollToBottom = () => {
      if (messageListRef.value) {
        messageListRef.value.scrollTop = messageListRef.value.scrollHeight
      }
    }

    // 切换调试模式
    const toggleDebugMode = () => {
      debugMode.value = !debugMode.value
    }

    // 格式化消息内容
    const formatMessage = (message) => {
      if (!message) return ''

      if (message.user_message) {
        return message.query || ''
      } else {
        return message.answer || ''
      }
    }

    // 从URL中获取文件名
    const getFileNameFromUrl = (url) => {
      if (!url) return ''

      try {
        // 移除查询参数
        const urlWithoutParams = url.split('?')[0]

        // 从URL中提取文件名
        const parts = urlWithoutParams.split('/')
        return parts[parts.length - 1] || 'file'
      } catch (e) {
        console.error('获取文件名失败:', e)
        return 'file'
      }
    }

    // 预览图片
    const previewImage = (url) => {
      if (!url) return

      try {
        // 创建一个新窗口查看图片
        window.open(url, '_blank')
      } catch (e) {
        console.error('预览图片失败:', e)
      }
    }

    // 是否是最后一条助手消息
    const isLastAssistantMessage = (index) => {
      if (index < 0 || !props.messages || index >= props.messages.length) return false

      // 从当前消息向后查找，看看是否有更靠后的助手消息
      for (let i = props.messages.length - 1; i >= 0; i--) {
        const msg = props.messages[i]
        // 只有当前消息是助手消息且是最后一条非用户消息时，才返回true
        if (!msg.user_message) {
          return i === index
        }
      }

      return false
    }

    // 是否是最后一条用户消息
    const isLastUserMessage = (index) => {
      if (index < 0 || !props.messages || index >= props.messages.length) return false

      // 从当前消息向后查找，看看是否有更靠后的用户消息
      for (let i = props.messages.length - 1; i >= 0; i--) {
        const msg = props.messages[i]
        if (msg.user_message) {
          return i === index
        }
      }

      return false
    }

    // 是否最后一条用户消息已经有助手回复
    const hasAssistantReply = (index) => {
      if (index < 0 || !props.messages || index >= props.messages.length) return false

      // 检查该用户消息之后是否有助手回复
      for (let i = index + 1; i < props.messages.length; i++) {
        if (!props.messages[i].user_message) {
          return true
        }
      }

      return false
    }

    // 根据接收时间对消息部分进行排序
    const getOrderedParts = (message) => {
      if (!message || !message.streamParts || message.streamParts.length === 0) {
        return []
      }

      // 复制数组以避免修改原始数据
      const parts = [...message.streamParts]

      // 根据接收时间排序
      return parts.sort((a, b) => a.receivedAt - b.receivedAt)
    }

    // 格式化工具输入，使其更可读
    const formatToolInput = (input) => {
      if (!input) return ''

      try {
        // 尝试解析为JSON并格式化
        const parsed = safeParseJSON(input)
        if (parsed) {
          return JSON.stringify(parsed, null, 2)
        }

        return input
      } catch (e) {
        return input
      }
    }

    // 获取消息中的文件
    const getFileById = (message, fileId) => {
      if (!message || !message.message_files || !fileId) return null

      return message.message_files.find(file => file.id === fileId)
    }

    // 检查工具调用是否为空
    const isEmptyToolCall = (part) => {
      if (!part) return true

      // 只检查是否有内容，不再检查工具名称
      if (!part.content && !part.tool_input && !getToolInputFromContent(part) && !part.observation && !getObservationFromContent(part)) {
        return true
      }

      return false
    }

    // 辅助函数：转换ASCII码为中文 - 不再使用该函数
    const convertAsciiToChinese = (text) => {
      return text
    }

    // 检查最后一条用户消息是否有助手回复
    const hasAssistantReplyToLastUserMessage = () => {
      if (!props.messages || props.messages.length === 0) return false

      // 寻找最后一条用户消息
      let lastUserMsgIndex = -1
      for (let i = props.messages.length - 1; i >= 0; i--) {
        if (props.messages[i].user_message) {
          lastUserMsgIndex = i
          break
        }
      }

      if (lastUserMsgIndex === -1) return false

      // 检查该用户消息之后是否有助手回复
      return hasAssistantReply(lastUserMsgIndex)
    }

    // 处理文件预览
    const handleFilePreview = (file) => {
      if (!file) return

      emit('preview-file', {
        id: file.id,
        url: file.url,
        name: getFileNameFromUrl(file.url),
        type: file.type || getFileMimeTypeFromUrl(file.url)
      })
    }

    // 获取文件MIME类型
    const getFileMimeTypeFromUrl = (url) => {
      if (!url) return ''

      try {
        const extension = url.split('.').pop().toLowerCase()

        const mimeTypes = {
          'jpg': 'image/jpeg',
          'jpeg': 'image/jpeg',
          'png': 'image/png',
          'gif': 'image/gif',
          'pdf': 'application/pdf',
          'doc': 'application/msword',
          'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'xls': 'application/vnd.ms-excel',
          'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'txt': 'text/plain',
          'json': 'application/json'
        }

        return mimeTypes[extension] || ''
      } catch (e) {
        console.error('获取文件MIME类型失败:', e)
        return ''
      }
    }

    // 获取文件类型
    const getFileType = (file) => {
      if (!file) return 'unknown'

      const name = getFileNameFromUrl(file.url)
      const mimeType = file.type || getFileMimeTypeFromUrl(file.url)

      // 图片
      if (mimeType.startsWith('image/') || /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(name)) {
        return 'image'
      }
      // Word文档
      else if (/\.(docx?)$/i.test(name)) {
        return 'docx'
      }
      // Excel
      else if (/\.(xlsx?|csv)$/i.test(name)) {
        return 'excel'
      }
      // PDF
      else if (/\.pdf$/i.test(name) || mimeType === 'application/pdf') {
        return 'pdf'
      }
      // JSON
      else if (/\.json$/i.test(name) || mimeType === 'application/json') {
        return 'json'
      }
      // 文本文件
      else if (/\.(txt|md|log)$/i.test(name) || mimeType === 'text/plain') {
        return 'text'
      }

      return 'unknown'
    }

    // 处理模式切换
    const handleChangeMode = (mode) => {
      emit('change-mode', mode);
    }

    // 检查是否应该显示光标
    const shouldShowCursor = (message) => {
      // 只在以下情况显示光标:
      // 1. 当前消息ID与streamingMessageId匹配且对应会话ID匹配
      // 2. 新对话且是最新会话(第一条历史)

      // 情况1：有明确的streamingMessageId时，精确匹配
      if (props.streamingMessageId &&
        message.id === props.streamingMessageId &&
        message.conversation_id === props.streamingConversationId) {
        return true;
      }

      // 情况2：没有streamingMessageId但正在streaming，且消息在最新会话中
      // 这需要判断消息是否属于最新会话(latestConversationId)
      if (!props.streamingMessageId &&
        props.streaming &&
        !message.waiting_for_response &&
        window.__DIFY_LATEST_CONVERSATION_ID__ === message.conversation_id) {
        return true;
      }

      // 其他情况不显示光标
      return false;
    }

    return {
      messageListRef,
      debugMode,
      toggleDebugMode,
      formatMarkdownText,
      emitExampleQuestion,
      toggleTool,
      isToolExpanded,
      getOrderedParts,
      formatToolInput,
      getFileNameFromUrl,
      isLastAssistantMessage,
      isLastUserMessage,
      hasAssistantReply,
      handleFilePreview,
      getFileType,
      getFileById,
      getToolInputFromContent,
      getObservationFromContent,
      isEmptyToolCall,
      hasAssistantReplyToLastUserMessage,
      handleRouterClick,
      handleChangeMode,
      shouldShowCursor,
      mergedExampleQuestions,
      welcomeData
    }
  }
}
</script>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  box-sizing: border-box;

  // 加载和空状态
  .loading-container,
  .empty-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: #23D9A7;
      animation: spin 1s linear infinite;
      margin-bottom: 10px;
    }

    .empty-icon {
      width: 50px;
      height: 50px;
      font-size: 48px;
      color: #ccc;
      margin-bottom: 10px;
    }
  }

  // 消息项
  .message-item {
    display: flex;
    margin-bottom: 15px;
    width: 100%;
    box-sizing: border-box;

    // 用户消息显示在右侧
    &.user-message {
      flex-direction: row-reverse;
      justify-content: flex-start;

      .message-content {
        max-width: 80%;
        align-self: flex-end;

        .user-text {
          background: linear-gradient(to right, #23D9A7, #4AB2EC);
          color: white;
          border-top-right-radius: 2px;
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
          padding: 10px 15px;
          display: inline-block;
          word-break: break-word;
          white-space: pre-wrap;
          overflow-wrap: break-word;
          max-width: 100%;
        }
      }
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
      flex-shrink: 0;
    }

    .assistant-avatar {
      background: url('@/assets/images/dify/GIF01.gif') no-repeat center;
      background-size: cover;
    }

    &.assistant-message {
      .message-content {
        max-width: 85%;
        background-color: white;
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        word-break: break-word;
        overflow-wrap: break-word;
      }
    }

    // 状态消息样式
    &.status-message {
      .status-content {
        background-color: rgba(255, 255, 255, 0.8);
        min-height: 30px;
        display: flex;
        align-items: center;
        padding: 8px 12px;
        min-width: 40px;
      }
    }

    // 等待响应状态
    &.waiting-message {
      .message-content {
        padding: 10px;
        min-height: 40px;

        .waiting-container {
          display: flex;
          align-items: center;
          justify-content: flex-start;
        }
      }
    }
  }
}

// 各种消息部分样式
.message-part {
  margin-bottom: 8px;

  .message-text-content {
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
    line-height: 1.5;
    max-width: 100%;
  }
}

// 工具调用部分
.agent-thought-part {
  margin: 8px 0;
}

// 文件部分
.file-part {
  .file-container {
    margin: 5px 0;

    .file-image {
      max-width: 200px;
      max-height: 200px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        opacity: 0.9;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }

    .file-document {
      display: flex;
      align-items: center;
      background-color: #f0f0f0;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: #e6e6e6;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      .document-icon {
        width: 20px;
        height: 20px;
        background: url('../../../assets/images/dify/document.png') no-repeat center;
        background-size: contain;
        margin-right: 5px;
      }

      span {
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 引用资源样式
.retriever-resources {
  margin-top: 10px;
  background-color: #f0f0f0;
  border-radius: 8px;
  padding: 10px;
  font-size: 12px;

  .resources-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .resource-item {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }

    .resource-title {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .resource-content {
      color: #666;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

@keyframes bubble-pulse {

  0%,
  100% {
    transform: scale(0.6);
    opacity: 0.2;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }
}

// 欢迎页面样式
.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;

  .welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 500px;
    text-align: center;

    .welcome-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
    }

    .welcome-title {
      margin: 10px 0 5px;
      color: #333;
      font-size: 24px;
      font-weight: bold;
    }

    .welcome-subtitle {
      margin: 0 0 20px;
      background-image: linear-gradient(to right, #23D9A7, #4AB2EC);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-size: 28px;
      font-weight: bold;
    }

    // 当前页面信息
    .current-page-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 6px;
      font-size: 14px;
      color: #666;

      .page-icon {
        color: #23D9A7;
        font-size: 16px;
      }
    }

    // 页面通知
    .page-notification {
      width: 100%;
      margin-bottom: 20px;
      background: linear-gradient(135deg, #E2F1FC 0%, #EBE6FF 100%);
      border-radius: 8px;
      padding: 16px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 4px 15px rgba(168, 192, 255, 0.2);

      .notification-title {
        font-size: 16px;
        font-weight: 600;
        color: #000000;
        margin-bottom: 12px;
      }

      .notification-content {
        .notification-summary {
          font-size: 14px;
          color: #000000;
          margin-bottom: 12px;

          .highlight-number {
            color: #FF0000;
            font-weight: bold;
          }
        }

        .notification-details {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .notification-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #000000;

            .notification-icon {
              font-size: 16px;

              &.urgent {
                color: #ff4757;
              }

              &.important {
                color: #ffa502;
              }

              &.minor {
                color: #3742fa;
              }
            }

            .highlight-number {
              color: #FF0000;
              font-weight: bold;
              margin-left: 4px;
            }
          }
        }
      }
    }

    // 建议问题
    .suggested-questions {
      width: 100%;

      .suggested-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        text-align: left;
      }

      .example-questions {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .question-card {
          background-color: #fff;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 12px 15px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

          &:hover {
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            border-color: #23D9A7;
          }

          .question-number {
            color: #ff4757;
            font-weight: bold;
            font-size: 14px;
            margin-right: 8px;
            min-width: 16px;
          }

          .question-text {
            color: #333;
            font-size: 14px;
            flex: 1;
            text-align: left;
          }

          .arrow-icon {
            color: #23D9A7;
            font-size: 16px;
          }
        }
      }
    }

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: #23D9A7;
      animation: spin 1s linear infinite;
      margin-bottom: 10px;
    }

    .empty-icon {
      width: 50px;
      height: 50px;
      font-size: 48px;
      color: #ccc;
      margin-bottom: 10px;
    }
  }

  .waiting-response {
    display: flex;
    justify-content: flex-start;
    margin-top: 8px;
    margin-left: 50px;

    .typing-bubbles {
      background-color: white;
      border-radius: 18px;
      padding: 8px 12px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }

  // 错误消息样式
  .error-message {
    color: #ff4d4f;
    padding: 5px 0;
    font-size: 14px;
  }

  // 等待响应容器
  .waiting-container {
    height: 24px;
    display: flex;
    align-items: center;
  }

  // 响应停止提示
  .response-stopped-notice {
    margin-top: 8px;
    padding: 6px 10px;
    background-color: #f8f9fa;
    border-left: 3px solid #ff4d4f;
    color: #ff4d4f;
    font-size: 12px;
    border-radius: 0 4px 4px 0;
  }

  // 打字机光标
  .typing-cursor {
    display: inline-block;
    width: 3px;
    height: 18px;
    background-color: #23D9A7;
    animation: blink 0.7s infinite;
    vertical-align: middle;
    margin-left: 2px;
  }

  // 等待响应气泡
  .typing-bubbles {
    display: flex;
    align-items: center;
    height: 20px;

    .bubble {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #23D9A7;
      margin-right: 4px;
      animation: bubble-pulse 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  // 特殊数据容器样式
  .special-data-container {
    margin-top: 10px;
    border-top: 1px solid #eaeaea;
    padding-top: 10px;
  }

  // 路由按钮容器
  .router-button-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
    margin-top: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #4AB2EC;

    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #333;
    }

    .router-btn {
      background: linear-gradient(to right, #23D9A7, #4AB2EC);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      cursor: pointer;
      margin-bottom: 8px;

      &:hover {
        opacity: 0.9;
      }
    }

    .router-info {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  // 图表容器
  .chart-container {
    margin-top: 10px;
    width: 100%;

    .chart-element {
      height: 300px;
      width: 100%;
      border-radius: 4px;
      overflow: hidden;
      background: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  // 调试按钮
  .debug-controls {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 100;

    .debug-button {
      background: rgba(255, 255, 255, 0.7);
      border: 1px solid #ccc;
      padding: 2px 5px;
      font-size: 12px;
      border-radius: 3px;
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
      }
    }
  }

  // 调试数据样式
  .debug-data {
    margin-top: 10px;
    padding: 10px;
    background: #f5f5f5;
    border: 1px dashed #ccc;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;

    pre {
      max-height: 300px;
      overflow: auto;
      margin-top: 5px;
    }
  }

  // 文件内容指示器
  .file-content-indicator {
    display: flex;
    align-items: center;
    margin-top: 5px;
    padding: 3px 6px;
    background-color: rgba(35, 217, 167, 0.1);
    border-radius: 4px;
    font-size: 12px;
    color: #23D9A7;

    .indicator-icon {
      margin-right: 5px;
    }

    .indicator-text {
      font-size: 12px;
    }
  }

  // Markdown表格样式
  .message-text-content {
    white-space: pre-wrap;
    word-break: break-word;

    &.plain-message {
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;
    }

    :deep(.markdown-table-row) {
      font-family: monospace;
      padding: 2px 0;
      border-bottom: 1px solid #eee;
      overflow-x: auto;
      display: block;
    }

    :deep(.markdown-table) {
      margin: 10px 0;
      overflow-x: auto;

      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;

        th,
        td {
          padding: 8px;
          text-align: left;
          border: 1px solid #ddd;
        }

        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }

        tr:nth-child(even) {
          background-color: #f9f9f9;
        }

        tr:hover {
          background-color: #f1f1f1;
        }
      }
    }

    :deep(.markdown-code-block) {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px;
      margin: 8px 0;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: 13px;
      color: #333;
      max-height: 300px;
      overflow-y: auto;
    }

    :deep(strong) {
      font-weight: bold;
    }

    :deep(em) {
      font-style: italic;
    }
  }

  // 历史消息中的agent_thoughts样式
  .history-agent-thoughts {
    margin-top: 10px;

    .agent-thought-part {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 调试消息样式
  .debug-message {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 10px;
    margin: 5px 0;
    overflow-x: auto;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
      font-family: monospace;
      font-size: 12px;
    }
  }

  // 添加文件列表样式
  .message-files {
    margin-top: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;

    .files-title {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 8px;
      color: #606266;
    }

    .files-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .file-item {
        display: flex;
        align-items: center;
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 6px 10px;
        cursor: pointer;

        &:hover {
          background-color: #f0f0f0;
          border-color: #c0c0c0;
        }

        .file-icon {
          margin-right: 6px;
          color: #606266;
        }

        .file-name {
          font-size: 13px;
          color: #303133;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 150px;
        }
      }
    }
  }
}
</style>