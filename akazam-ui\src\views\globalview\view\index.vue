<template>
  <div class="app-container pageGlobalview none  nopm" v-loading="loading">
    <div class="pageGlobalviewtitle">贯彻落实集团0-1-5-10运维要求</div>
    <div class="flex-box-center posi">
      <div class="downimglist flex-box-center">
        <div class="item">
          <div class="tp nb flex-box-center"><img src="@/assets/images/globalview/v4/nb.png" alt=""></div>
          <div class="dn flex-box-center"><img src="@/assets/images/globalview/v4/n0.png" alt=""></div>
        </div>
        <div class="item">
          <div class="tp ns flex-box-center"><img src="@/assets/images/globalview/v4/ns.png" alt=""></div>
          <div class="dn flex-box-center"><img src="@/assets/images/globalview/v4/n1.png" alt=""></div>
        </div>
        <div class="item">
          <div class="tp ns flex-box-center"><img src="@/assets/images/globalview/v4/ns.png" alt=""></div>
          <div class="dn flex-box-center"><img src="@/assets/images/globalview/v4/n5.png" alt=""></div>
        </div>
        <div class="item">
          <div class="tp ns flex-box-center"><img src="@/assets/images/globalview/v4/ns.png" alt=""></div>
          <div class="dn flex-box-center"><img src="@/assets/images/globalview/v4/n10.png" alt=""></div>
        </div>
      </div>
      <div class="list " v-for="(item, i) in l" :key="item.c">
        <div class="class-item">
          <ul class="class-ul">
            <li v-for="(s, j) in item.s" :key="j" class="flex-box" :class="{ 'cur': s.link || s.type }"
              @click="link(s)">
              <div class="icon" :class="`icon-${item.c}-${j + 1}`"></div>
              <div class="text-wb"
                :class="{ 'text-bw': s.name == '智能问答数字人' || s.name == '割接数字人' || s.name == '安全数字人' || s.name == '安全数字人' || s.name == '故障处理数字人', 'text-no': !s.link && !s.type }">
                {{ s.name }}</div>
            </li>
          </ul>
          <div class="class-item-bg" :class="'icon-' + (i + 1)"></div>
          <div class="class-title">
            <div class="class-title-text" :class="`icon-${i + 1}`"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="globalview">

import { ref } from 'vue'
const router = useRouter()
const currentQuestions = ref([]) // 当前问题列表

const l = [
  {
    n: '监控告警', c: 1,
    s: [
      {
        name: '割接数字人',
        type: '对话框',
        link: '',
        questions: [
          'WAF割接辅助',
          '交换机应急端口开通辅助'
        ]
      },
      {
        name: '安全数字人',
        type: '对话框',
        link: '',
        questions: [
          '查询指定WAF上的业务可用性情况'
        ]
      },
      {
        name: '智能问答数字人',
        type: '对话框',
        link: '',
        questions: [
          '查询设备详细信息',
          '查询指定设备指标信息',
          '平台功能导航'
        ]
      },
      { name: '自动化巡检', type: '', link: '/automate/automate-overview' },
      { name: '自动化开通', type: '', link: '' },
      { name: '自动化验收', type: '', link: '' },
    ]
  },
  {
    n: '主动维护', c: 2, s: [
      { name: '故障预判', type: '', link: '/scene5' },
      { name: '业务风险模型', type: '', link: '' },
    ]
  },
  {
    n: '风险识别', c: 3, s: [
      {
        name: '端到端探测',
        type: '',
        link: '/faultDetection/probe/npsimulate',
      },
      {
        name: '重要业务监控大屏',
        type: '',
        link: '/datawatch/project_monitor/doctor',
      },
      { name: '健康度识别', type: '', link: '' },

      // { name: '性能下限识别', type: '', link: '/scene4' },
      // { name: '风险预警', type: '', link: '' },
    ]
  },
  {
    n: '排障定位', c: 4, s: [
      {
        name: '故障处理数字人',
        type: '对话框',
        link: '',
        questions: [
          '时通时不通故障一键处理'
        ]
      },
      { name: '故障探测', type: '', link: '/faultDetection/allDetection/allDetection' },
      { name: 'CMDB', type: '', link: '/resourcemanage/tree' },
      { name: '故障关联分析', type: '', link: '' },
    ]
  },
  {
    n: '自动恢复', c: 5, s: [
      { name: '脚本仓库', type: '', link: '/automate/script/automate-script-index' },
      { name: '一键自动恢复切换', type: '', link: '' },
      { name: '故障自动恢复', type: '', link: '' },
    ]
  }
]

const link = (row) => {
  if (row.link) {
    router.push({
      path: row.link
    })
    return false
  }
  if (row.type && row.type == '对话框') {
    // 获取问题列表
    currentQuestions.value = row.questions || []

    // 通过全局事件分发示例问题
    if (currentQuestions.value && currentQuestions.value.length > 0) {
      // 先关闭聊天窗口（如果已打开）
      window.dispatchEvent(new CustomEvent('dify-close-chat'))

      // 设置延时确保关闭完成后再打开
      setTimeout(() => {
        // 先设置示例问题
        window.dispatchEvent(new CustomEvent('dify-set-example-questions', {
          detail: currentQuestions.value
        }))

        // 然后触发打开聊天窗口事件
        window.dispatchEvent(new CustomEvent('dify-toggle-chat'))
      }, 300)
    }
  }
}
</script>

<style lang="scss" scoped>
.pageGlobalview {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  transform: scale(0.7);
}

.pageGlobalviewtitle {
  font-family: MicrosoftYaHeiUI, MicrosoftYaHeiUI;
  font-weight: bold;
  font-size: 60px;
  color: #00D9FF;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-bottom: 20px;
}

.list {
  background-image: url('@/assets/images/globalview/v4/list-item.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top center;
  width: 320px;
  height: 700px;
  margin: 0 10px;
  box-sizing: border-box;
  padding-top: 100px;
}

.class-title {
  background-image: url('@/assets/images/globalview/v4/list-item-down.png');
  background-size: cover;
  width: 290px;
  height: 85px;
  margin: 10px auto 0 auto;
  box-sizing: border-box;
  padding-top: 20px;

  .class-title-text {
    background-size: cover;
    width: 182px;
    height: 50px;
    margin: 0 auto 0 auto;

    &.icon-1 {
      background-image: url('@/assets/images/globalview/v4/title-1.png');
    }

    &.icon-2 {
      background-image: url('@/assets/images/globalview/v4/title-2.png');
    }

    &.icon-3 {
      background-image: url('@/assets/images/globalview/v4/title-3.png');
    }

    &.icon-4 {
      background-image: url('@/assets/images/globalview/v4/title-4.png');
    }

    &.icon-5 {
      background-image: url('@/assets/images/globalview/v4/title-5.png');
    }
  }
}

.class-ul {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 350px;
}

.class-item-bg {
  width: 118px;
  height: 143px;
  background-size: cover;
  background-repeat: no-repeat;
  margin: 0 auto;

  &.icon-1 {
    background-image: url('@/assets/images/globalview/v4/icon-b-1.png');
  }

  &.icon-2 {
    background-image: url('@/assets/images/globalview/v4/icon-b-2.png');
  }

  &.icon-3 {
    background-image: url('@/assets/images/globalview/v4/icon-b-3.png');
  }

  &.icon-4 {
    background-image: url('@/assets/images/globalview/v4/icon-b-4.png');
  }

  &.icon-5 {
    background-image: url('@/assets/images/globalview/v4/icon-b-5.png');
  }
}

.class-ul li {
  font-size: 16px;
  margin: 10px 0;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 20px;
  cursor: not-allowed;

  &.cur {
    cursor: pointer;
  }

  .icon {
    width: 40px;
    height: 40px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 5px;
    margin-left: 55px;

    &.icon-1-1 {
      background-image: url('@/assets/images/globalview/v4/icon-1-1.png');
    }

    &.icon-1-2 {
      background-image: url('@/assets/images/globalview/v4/icon-1-2.png');
    }

    &.icon-1-3 {
      background-image: url('@/assets/images/globalview/v4/icon-1-3.png');
    }

    &.icon-1-4 {
      background-image: url('@/assets/images/globalview/v4/icon-1-4.png');
    }

    &.icon-1-5 {
      background-image: url('@/assets/images/globalview/v4/icon-1-5.png');
    }

    &.icon-1-6 {
      background-image: url('@/assets/images/globalview/v4/icon-1-6.png');
    }

    &.icon-2-1 {
      background-image: url('@/assets/images/globalview/v4/icon-2-1.png');
    }

    &.icon-2-2 {
      background-image: url('@/assets/images/globalview/v4/icon-2-2.png');
    }

    &.icon-2-3 {
      background-image: url('@/assets/images/globalview/v4/icon-2-3.png');
    }

    &.icon-2-4 {
      background-image: url('@/assets/images/globalview/v4/icon-2-4.png');
    }

    &.icon-2-5 {
      background-image: url('@/assets/images/globalview/v4/icon-2-5.png');
    }

    &.icon-3-1 {
      background-image: url('@/assets/images/globalview/v4/icon-3-1.png');
    }

    &.icon-3-2 {
      background-image: url('@/assets/images/globalview/v4/icon-3-2.png');
    }

    &.icon-3-3 {
      background-image: url('@/assets/images/globalview/v4/icon-3-3.png');
    }

    &.icon-3-4 {
      background-image: url('@/assets/images/globalview/v4/icon-3-4.png');
    }

    &.icon-3-5 {
      background-image: url('@/assets/images/globalview/v4/icon-3-5.png');
    }

    &.icon-3-1 {
      background-image: url('@/assets/images/globalview/v4/icon-3-1.png');
    }

    &.icon-4-1 {
      background-image: url('@/assets/images/globalview/v4/icon-4-1.png');
    }

    &.icon-4-2 {
      background-image: url('@/assets/images/globalview/v4/icon-4-2.png');
    }

    &.icon-4-3 {
      background-image: url('@/assets/images/globalview/v4/icon-4-3.png');
    }

    &.icon-4-4 {
      background-image: url('@/assets/images/globalview/v4/icon-4-4.png');
    }

    &.icon-5-1 {
      background-image: url('@/assets/images/globalview/v4/icon-5-1.png');
    }

    &.icon-5-2 {
      background-image: url('@/assets/images/globalview/v4/icon-5-2.png');
    }

    &.icon-5-3 {
      background-image: url('@/assets/images/globalview/v4/icon-5-3.png');
    }
  }

  .text-wb {
    font-size: 22px;
    color: #0080FF;
    font-weight: bold;
    font-size: 22px;
    line-height: 40px;
    text-align: left;
  }

  .text-bw {
    font-size: 22px;
    font-weight: bold;
    font-size: 22px;
    line-height: 40px;
    text-align: left;
    color: #00D9FF;
  }

  .text-no {
    font-size: 22px;
    color: #8EB2E4;
    font-weight: bold;
    font-size: 22px;
    line-height: 40px;
    text-align: left;
  }

}

.class-ul li.cur:hover {
  transform: scale(1.04);
  border-radius: 8px;
  transition: all .2s;
}

.posi {
  position: relative;

  .downimglist {
    position: absolute;
    top: 710px;
    left: 0;
    width: 100%;
    z-index: 100;

    .item {
      margin: 0 23px;

      .tp {}

      .dn {
        margin-top: 10px;
      }
    }
  }
}

@media screen and (max-width: 1600px) {
  .list {
    width: 280px;
    height: 650px;
    transform: scale(0.95);
    margin: 0 5px;
    padding-top: 90px;
  }

  .class-title {
    width: 260px;
    height: 130px;
  }

  .class-title-text {
    width: 160px;
    height: 45px;
  }

  .class-ul {
    height: 320px;
    overflow-y: auto;
  }

  .class-item-bg {
    width: 100px;
    height: 120px;
  }

  .class-ul li .icon {
    width: 35px;
    height: 35px;
    margin-left: 40px;
  }
}

@media screen and (max-width: 1440px) {
  .list {
    width: 260px;
    height: 620px;
    transform: scale(0.9);
    padding-top: 80px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 20px;
  }
}

@media screen and (max-width: 1370px) {
  .pageGlobalview {
    padding-top: 0px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  .pageGlobalviewtitle{
    font-size: 40px;
    margin-bottom: 0;
  }

  .list {
    width: 240px;
    height: 580px;
    transform: scale(0.85);
    margin: 0 3px 10px;
    padding-top: 70px;
  }

  .class-title {
    width: 240px;
    height: 70px;
  }

  .class-ul {
    height: 300px;
  }

  .class-ul li {
    margin: 5px 0 15px;
  }

  .class-ul li .icon {
    width: 32px;
    height: 32px;
    margin-left: 30px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 18px;
    line-height: 32px;
  }

  .posi {
  position: relative;
  transform: translateY(-50px);

  .downimglist {
    position: absolute;
    top: 520px;
    left: 0;
    width: 100%;
    z-index: 100;

    .item {
      margin: 0 10px;

      .nb {
        width: 460px;
      }
      .ns{
        width: 225px;
      }

      .dn {
        margin-top: 10px;
      }
    }
  }
}
}

@media screen and (max-width: 1200px) {
  .list {
    width: 220px;
    height: 550px;
    transform: scale(0.8);
    padding-top: 65px;
  }

  .class-title {
    width: 200px;
    height: 110px;
  }

  .class-ul {
    height: 280px;
  }

  .class-ul li .icon {
    width: 30px;
    height: 30px;
    margin-left: 25px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 16px;
    line-height: 30px;
  }
}

@media screen and (max-width: 1024px) {
  .pageGlobalviewtitle {
    font-size: 48px;
    margin-bottom: 15px;
  }

  .pageGlobalview {
    padding: 20px 10px;
  }

  .list {
    width: 200px;
    height: 500px;
    transform: scale(0.75);
    margin: 0 2px 8px;
    padding-top: 60px;
  }

  .class-title {
    width: 180px;
    height: 100px;
    padding-top: 15px;
  }

  .class-title-text {
    width: 140px;
    height: 40px;
  }

  .class-ul {
    height: 250px;
  }

  .class-ul li {
    margin: 3px 0 12px;
  }

  .class-ul li .icon {
    width: 28px;
    height: 28px;
    margin-left: 20px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 14px;
    line-height: 28px;
  }

  .class-item-bg {
    width: 80px;
    height: 100px;
  }

  .posi .downimglist {
    top: 520px;

    .item {
      margin: 0 15px;

      .tp img,
      .dn img {
        width: 60px;
        height: auto;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .pageGlobalviewtitle {
    font-size: 36px;
    margin-bottom: 10px;
  }

  .pageGlobalview {
    padding: 15px 5px;
  }

  .list {
    width: 160px;
    height: 420px;
    transform: scale(0.65);
    margin: 0 1px 5px;
    padding-top: 50px;
  }

  .class-title {
    width: 140px;
    height: 80px;
    padding-top: 10px;
  }

  .class-title-text {
    width: 110px;
    height: 35px;
  }

  .class-ul {
    height: 200px;
  }

  .class-ul li {
    margin: 2px 0 8px;
  }

  .class-ul li .icon {
    width: 24px;
    height: 24px;
    margin-left: 15px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 12px;
    line-height: 24px;
  }

  .class-item-bg {
    width: 60px;
    height: 80px;
  }

  .posi .downimglist {
    top: 440px;

    .item {
      margin: 0 10px;

      .tp img,
      .dn img {
        width: 40px;
        height: auto;
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .pageGlobalviewtitle {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .pageGlobalview {
    padding: 10px 2px;
  }

  .list {
    width: 140px;
    height: 380px;
    transform: scale(0.55);
    margin: 0 1px 3px;
    padding-top: 40px;
  }

  .class-title {
    width: 120px;
    height: 70px;
    padding-top: 8px;
  }

  .class-title-text {
    width: 90px;
    height: 30px;
  }

  .class-ul {
    height: 180px;
  }

  .class-ul li {
    margin: 1px 0 6px;
  }

  .class-ul li .icon {
    width: 20px;
    height: 20px;
    margin-left: 10px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 10px;
    line-height: 20px;
  }

  .class-item-bg {
    width: 50px;
    height: 70px;
  }

  .posi .downimglist {
    top: 400px;

    .item {
      margin: 0 8px;

      .tp img,
      .dn img {
        width: 30px;
        height: auto;
      }
    }
  }
}

@media screen and (max-width: 375px) {
  .pageGlobalviewtitle {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .pageGlobalview {
    padding: 8px 1px;
  }

  .list {
    width: 120px;
    height: 340px;
    transform: scale(0.5);
    margin: 0 1px 2px;
    padding-top: 35px;
  }

  .class-title {
    width: 100px;
    height: 60px;
    padding-top: 5px;
  }

  .class-title-text {
    width: 75px;
    height: 25px;
  }

  .class-ul {
    height: 160px;
  }

  .class-ul li {
    margin: 1px 0 4px;
  }

  .class-ul li .icon {
    width: 18px;
    height: 18px;
    margin-left: 8px;
  }

  .class-ul li .text-wb,
  .class-ul li .text-bw,
  .class-ul li .text-no {
    font-size: 9px;
    line-height: 18px;
  }

  .class-item-bg {
    width: 40px;
    height: 60px;
  }

  .posi .downimglist {
    top: 360px;

    .item {
      margin: 0 5px;

      .tp img,
      .dn img {
        width: 25px;
        height: auto;
      }
    }
  }
}
</style>
