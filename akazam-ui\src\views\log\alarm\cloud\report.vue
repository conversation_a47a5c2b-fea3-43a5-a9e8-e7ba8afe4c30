<template>
  <div class="app-container report-container" v-loading="loading" element-loading-text="正在加载报告数据..."
    element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
    <!-- 告警基本信息 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h4>告警基本信息</h4>
        </div>
      </template>
      <el-descriptions :column="2" class="alarm-info-descriptions">
        <el-descriptions-item label="告警名称：">{{ reportData.alarmInfo?.alarmName }}</el-descriptions-item>
        <el-descriptions-item label="告警源：">{{ reportData.alarmInfo?.alarmSource }}</el-descriptions-item>
        <el-descriptions-item label="告警编号：">{{ reportData.alarmInfo?.alarmSN }}</el-descriptions-item>
        <el-descriptions-item label="告警时间：">{{ reportData.alarmInfo?.alarmTime }}</el-descriptions-item>
        <el-descriptions-item label="告警源系统类型：">{{ reportData.alarmInfo?.alarmSourceSystemType }}</el-descriptions-item>
        <el-descriptions-item label="告警源系统名称：">{{ reportData.alarmInfo?.alarmSourceSystemName }}</el-descriptions-item>
        <el-descriptions-item label="告警对象类型：">{{ reportData.alarmInfo?.alarmObjectType }}</el-descriptions-item>
        <el-descriptions-item label="告警位置信息：">{{ reportData.alarmInfo?.alarmLocationInfo }}</el-descriptions-item>
        <el-descriptions-item label="告警附加信息：">{{ reportData.alarmInfo?.alarmAdditionInfo }}</el-descriptions-item>
        <el-descriptions-item label="设备类型：">{{ reportData.alarmInfo?.meType }}</el-descriptions-item>
        <el-descriptions-item label="制造商：">{{ reportData.alarmInfo?.manufacturer }}</el-descriptions-item>
        <el-descriptions-item label="型号：">{{ reportData.alarmInfo?.model }}</el-descriptions-item>
        <!-- TODO 入库时间 -->
        <el-descriptions-item label="入库时间：">{{ reportData.alarmInfo?.createTime }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 相关告警信息 -->
    <el-card class="section-card" shadow="never"
      v-if="reportData.relationAlarmInfoList && reportData.relationAlarmInfoList.length > 0">
      <template #header>
        <div class="card-header">
          <h4>相关告警信息 ({{ reportData.relationAlarmInfoList.length }}条)</h4>
        </div>
      </template>

      <!-- 告警信息容器 -->
      <div class="relation-alarm-container">
        <div class="alarm-tabs">
          <el-tabs v-model="activeAlarmTab" type="card" class="alarm-tabs-wrapper">
            <el-tab-pane v-for="(alarm, index) in reportData.relationAlarmInfoList" :key="index"
              :label="`${alarm.alarmName || '未知告警'} - ${alarm.alarmSource || '未知源'}`" :name="index">
              <div class="alarm-content">
                <div class="alarm-header">
                  <span class="alarm-time">{{ alarm.alarmTime }}</span>
                </div>
                <el-descriptions :column="2" class="relation-alarm-descriptions">
                  <el-descriptions-item label="告警名称：">{{ alarm.alarmName }}</el-descriptions-item>
                  <el-descriptions-item label="告警源：">{{ alarm.alarmSource }}</el-descriptions-item>
                  <el-descriptions-item label="告警编号：">{{ alarm.alarmSN }}</el-descriptions-item>
                  <el-descriptions-item label="告警时间：">{{ alarm.alarmTime }}</el-descriptions-item>
                  <el-descriptions-item label="告警源系统类型：">{{ alarm.alarmSourceSystemType }}</el-descriptions-item>
                  <el-descriptions-item label="告警源系统名称：">{{ alarm.alarmSourceSystemName }}</el-descriptions-item>
                  <el-descriptions-item label="告警对象类型：">{{ alarm.alarmObjectType }}</el-descriptions-item>
                  <el-descriptions-item label="告警位置信息：">{{ alarm.alarmLocationInfo }}</el-descriptions-item>
                  <el-descriptions-item label="告警附加信息：">{{ alarm.alarmAdditionInfo }}</el-descriptions-item>
                  <el-descriptions-item label="设备类型：">{{ alarm.meType }}</el-descriptions-item>
                  <el-descriptions-item label="制造商：">{{ alarm.manufacturer }}</el-descriptions-item>
                  <el-descriptions-item label="型号：">{{ alarm.model }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>
    <!-- 智能处理结果 -->
    <el-card class="section-card" shadow="never">
      <template #header>
        <div class="header-content">
          <div class="card-header flex-box-between">
            <h4>智能处理结果</h4>
          </div>
        </div>
      </template>
      <div class="handle-Ai-result">
        {{ disposeResult }}
      </div>
    </el-card>
    <!-- 处理结果和分析 - Tabs切换 -->
    <div v-if="hasAnyTabContent">
      <el-tabs v-model="activeMainTab" class="main-tabs-wrapper">
        <!-- 智能处理结果 -->
        <el-tab-pane label="智能处理结果" name="disposeResult" v-if="disposeResult && disposeResult.trim()">
          <el-card class="section-card" shadow="never">
            <div class="ai-analysis">
              <div class="ai-response">
                <div class="ai-avatar">
                  <img src="@/assets/images/dify/GIF01.gif" alt="AI助手" />
                </div>
                <div class="ai-content">
                  <p v-html="disposeResult.replace(/\n/g, '<br>')"></p>
                </div>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- 相关诊断 -->
        <el-tab-pane label="相关诊断" name="handleResult" v-if="reportData && reportData.handleResult && reportData.handleResult.length > 0">
          <el-card class="section-card" shadow="never">
            <div class="handle-results">
              <div v-for="(step, index) in reportData.handleResult" :key="index" class="handle-step">
                <div class="step-header">
                  <div class="step-number">{{ index + 1 }}</div>
                  <h5 class="step-title">{{ step.description }}</h5>
                </div>
                <div class="step-content">
                  <!-- 表单类型 -->
                  <div v-if="step.type === 'form'" class="form-content">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item v-for="(item, itemIndex) in step.value" :key="itemIndex"
                        :label="item.formName">
                        {{ item.formValue || '-' }}
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>

                  <!-- 路由类型 -->
                  <div v-else-if="step.type === 'router'" class="router-content">
                    <el-button type="primary" @click="handleRouterAction(step.value)" :style="{
                      display: step.value.display === 'bottom' ? 'block' : 'inline-block',
                      marginTop: step.value.display === 'bottom' ? '10px' : '0'
                    }">
                      导出数据
                    </el-button>
                  </div>

                  <!-- 表格类型 -->
                  <div v-else-if="step.type === 'table'" class="table-content">
                    <el-table :data="step.rows || []" stripe style="width: 100%" max-height="400">
                      <el-table-column v-for="header in step.headers || []" :key="header.field" :prop="header.field"
                        :label="header.name" :width="getColumnWidth(header.field)" show-overflow-tooltip>
                        <template #default="scope" v-if="header.field === 'status'">
                          <el-tag :type="getStatusType(scope.row[header.field])">
                            {{ scope.row[header.field] }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  <!-- 图表类型 -->
                  <div v-else-if="step.type === 'chart'" class="chart-content">
                    <div class="step-chart" :data-chart-index="index" style="height: 300px;"></div>
                  </div>

                  <!-- JSON/代码类型 -->
                  <div v-else-if="step.type === 'json'" class="json-content">
                    <!-- 如果是字符串（脚本命令），使用 pre 标签显示 -->
                    <div v-if="typeof step.value === 'string'" class="script-content">
                      <div class="script-header">
                        <span class="script-title">脚本内容</span>
                        <el-button type="text" size="small" @click="copyToClipboard(processScriptContent(step.value))">
                          <el-icon>
                            <Document />
                          </el-icon>
                          复制
                        </el-button>
                      </div>
                      <pre class="script-body">{{ processScriptContent(step.value) }}</pre>
                    </div>
                    <!-- 如果是对象，使用 json-viewer 组件显示 -->
                    <json-viewer v-else boxed copyable theme="my-awesome-json-theme" :expand-depth="5" :expanded="true"
                      :value="step.value">
                    </json-viewer>
                  </div>

                  <!-- 默认类型 -->
                  <div v-else class="default-content">
                    <pre>{{ JSON.stringify(step.value, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- 关联分析 -->
        <el-tab-pane label="关联分析" name="relationAnalysis"
          v-if="hasTopologyData || (relationAnalysisData.coFluxList && relationAnalysisData.coFluxList.length > 0)">
          <el-card class="section-card" shadow="never">
            <div class="topology-container">
              <div ref="topologyChart" class="topology-chart"></div>
            </div>
            <div class="relation-analysis"
              v-if="relationAnalysisData.coFluxList && relationAnalysisData.coFluxList.length > 0">
              <!-- 顶部标签页 -->
              <div class="relation-tabs">
                <div class="tab-item" v-for="(tab, index) in relationTabs" :key="index"
                  :class="{ active: activeTab === index }" @click="activeTab = index">
                  {{ tab.name }}
                </div>
              </div>

              <!-- 关联数据表格 -->
              <div class="relation-table-container">
                <el-table :data="currentTabRelations" style="width: 100%" stripe>
                  <el-table-column prop="deviceType" label="设备类型" width="120" />
                  <el-table-column prop="ip" label="IP" width="150" />
                  <el-table-column prop="metricName" label="关联指标" width="150" />
                  <el-table-column label="指标趋势" width="200">
                    <template #default="scope">
                      <div class="mini-chart-container" :data-chart-id="`miniChart_${scope.$index}`"
                        @click="showDetailChart(scope.row.rawData, '关联指标详情', scope.$index)">
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="correlation" label="关联系数" width="120">
                    <template #default="scope">
                      <span>{{ scope.row.point }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- 处置建议 -->
        <el-tab-pane label="处置建议" name="modelSuggest" v-if="reportData.modelSuggest && reportData.modelSuggest.trim()">
          <el-card class="section-card" shadow="never">
            <div class="ai-analysis">
              <div class="ai-response">
                <div class="ai-avatar">
                  <img src="@/assets/images/dify/GIF01.gif" alt="AI助手" />
                </div>
                <div class="ai-content">
                  <p v-html="reportData.modelSuggest.replace(/\n/g, '<br>')"></p>
                </div>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- 备注 -->
        <el-tab-pane label="备注" name="remark" v-if="reportData.alarmInfo?.remark && reportData.alarmInfo.remark.trim()">
          <el-card class="section-card" shadow="never">
            <div class="handle-remark">
              {{ reportData.alarmInfo?.remark }}
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <!-- <el-button type="success" @click="handleAccept" class="accept-btn">采纳</el-button>
      <el-button type="danger" @click="handleReject" class="reject-btn">不采纳</el-button> -->
      <el-button @click="handleReturn" class="return-btn">返回</el-button>
    </div>

    <!-- 详细指标图表弹框 -->
    <el-dialog v-model="chartDialogVisible" :title="dialogData.metricName || '指标趋势详情'" width="80%"
      :before-close="handleCloseDialog">
      <div class="dialog-chart-container">
        <div ref="dialogChart" class="dialog-chart"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Report">
const { proxy } = getCurrentInstance();

import { ref, onMounted, computed, nextTick, onBeforeUnmount, watch, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import { getReport, getRelationAnalysis } from "@/api/log/alarm_info";
import * as echarts from 'echarts';

const route = useRoute();
const router = useRouter();
const reportData = ref({});
const relationAnalysisData = ref({}); // 新增：关联分析数据
const topologyChart = ref(null);
let chartInstance = null;

// 配置常量 - 替代硬编码数据
const CHART_COLORS = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#9C27B0', '#FF9800', '#2196F3', '#4CAF50',
  '#FF5722', '#795548', '#607D8B', '#FFC107'
];

const CI_TYPE_MAP = {
  'user_vm': '虚拟机',
  'host': '宿主机',
  'switch': '交换机',
  'sys_server': '物理服务器',
  'firewall': '防火墙',
  'router': '路由器'
};

// 添加新的响应式变量
const chartDialogVisible = ref(false);
const dialogData = ref({});
let dialogChartInstance = null;
const metricCharts = ref({});
const stepChartInstances = ref(new Map()); // 存储步骤图表实例
const loading = ref(false);

// 新增：表格和标签页相关数据
const activeTab = ref(0);
const activeAlarmTab = ref(0);
const activeMainTab = ref('handleResult'); // 主要tabs的活动标签
const remarkContent = ref(''); // 备注内容
const relationTabs = computed(() => {
  // 使用新的关联分析数据结构 coFluxList
  if (!relationAnalysisData.value.coFluxList) return [];

  // 根据src.metricName去重创建标签页
  const uniqueMetrics = [...new Set(relationAnalysisData.value.coFluxList.map(item => item.src.metricName))];

  return uniqueMetrics.map((metricName, index) => {
    return {
      name: metricName,
      metricName: metricName,
      index: index
    };
  });
});
let mainRelationChartInstance = null;
const selectedRelations = ref([]);

// 计算是否有拓扑数据
const hasTopologyData = computed(() => {
  // 检查新的关联分析数据中的ci字段
  return (reportData.value.topology &&
    reportData.value.topology.ci &&
    (reportData.value.topology.ci.res_id ||
      reportData.value.topology.ci.id ||
      reportData.value.topology.ci.uuid ||
      reportData.value.topology.ci.res_name)) ||
    (relationAnalysisData.value.ci &&
      (relationAnalysisData.value.ci.resId ||
        relationAnalysisData.value.ci.ciName));
});

// 计算是否有任何tab内容
const hasAnyTabContent = computed(() => {
  return (disposeResult.value && disposeResult.value.trim()) ||
    (reportData.value.handleResult && reportData.value.handleResult.length > 0) ||
    (hasTopologyData.value || (relationAnalysisData.value.coFluxList && relationAnalysisData.value.coFluxList.length > 0)) ||
    (reportData.value.modelSuggest && reportData.value.modelSuggest.trim()) ||
    (reportData.value.alarmInfo?.remark && reportData.value.alarmInfo.remark.trim());
});

// 计算当前标签页的关联数据
const currentTabRelations = computed(() => {
  if (!relationAnalysisData.value.coFluxList || !relationTabs.value[activeTab.value]) return [];

  // 获取当前标签页对应的metricName
  const currentMetricName = relationTabs.value[activeTab.value].metricName;

  // 筛选出匹配的coFluxList数据
  const filteredRelations = relationAnalysisData.value.coFluxList.filter(item => item.src.metricName === currentMetricName);

  return filteredRelations.map((item, index) => {
    return {
      id: index,
      deviceType: item.src.ciType,  // 直接使用src.ciType
      ip: '-',    // 使用tar.resId作为标识
      metricName: item.tar.metricName,  // 不转换中文
      point: item.point,            // 关联系数不做处理
      correlation: item.correlation,
      rawData: item
    };
  });
});

// 初始化拓扑图表
const initTopologyChart = async () => {
  if (!topologyChart.value || !hasTopologyData.value) return;

  await nextTick();

  if (chartInstance) {
    chartInstance.dispose();
  }

  // 确保容器有正确的尺寸
  const container = topologyChart.value;
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    // 如果容器尺寸为0，设置默认尺寸
    container.style.width = '100%';
    container.style.height = '750px';
  }

  chartInstance = echarts.init(container);

  const graphData = prepareAdvancedGraphData();

  const option = {
    title: {
      show: false,
      text: '网络拓扑关系图',
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2c3e50'
      }
    },
    backgroundColor: '#fafafa',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#777',
      borderWidth: 1,
      textStyle: {
        color: '#fff'
      },
      formatter: function (params) {
        if (params.dataType === 'node') {
          const data = params.data;

          // 节点类型显示名称映射
          const nodeTypeMap = {
            'center': 'CI中心',
            'parent': '父级设备',
            'child': '子级设备'
          };

          const nodeTypeDisplay = nodeTypeMap[data.nodeType] || data.nodeType;
          const levelDisplay = data.level > 0 ? ` (第${data.level}层)` : '';

          let content = `<div style="max-width: 400px; line-height: 1.6;">
            <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #4CAF50;">${data.name}</div>
            <div style="margin-bottom: 6px;"><strong>设备类型:</strong> <span style="color: #FFC107;">${data.deviceType}</span></div>
            <div style="margin-bottom: 6px;"><strong>节点类型:</strong> <span style="color: #2196F3;">${nodeTypeDisplay}${levelDisplay}</span></div>`;

          if (data.ip && data.ip !== '-') content += `<div style="margin-bottom: 6px;"><strong>IP地址:</strong> ${data.ip}</div>`;
          if (data.status) content += `<div style="margin-bottom: 6px;"><strong>运行状态:</strong> <span style="color: ${data.status.includes('ACTIVE') || data.status.includes('normal') || data.status.includes('up') ? '#4CAF50' : '#F44336'};">${data.status}</span></div>`;
          if (data.manufacturer && data.manufacturer !== '未知' && data.manufacturer !== '-') {
            content += `<div style="margin-bottom: 6px;"><strong>制造商:</strong> ${data.manufacturer}</div>`;
          }
          if (data.model && data.model !== '未知' && data.model !== '-') {
            content += `<div style="margin-bottom: 6px;"><strong>设备型号:</strong> ${data.model}</div>`;
          }

          // 显示性能指标（如果有）
          if (data.metrics && Object.keys(data.metrics).length > 0) {
            content += `<div style="margin-top: 10px; padding-top: 8px; border-top: 1px solid #555;">
              <div style="font-weight: bold; margin-bottom: 4px; color: #FF9800;">性能指标:</div>`;
            for (const [key, value] of Object.entries(data.metrics)) {
              if (value) content += `<div style="margin-bottom: 2px;"><strong>${key}:</strong> ${value}</div>`;
            }
            content += `</div>`;
          }

          content += `<div style="margin-top: 8px; font-size: 12px; color: #999;">ID: ${data.id}</div></div>`;
          return content;
        }
        return '';
      }
    },
    legend: {
      show: false,
      orient: 'horizontal',
      bottom: 15,
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: '#333'
      },
      data: graphData.categories.map(cat => cat.name)
    },
    series: [
      {
        name: '拓扑关系图',
        type: 'graph',
        layout: 'none',  // 使用自定义布局
        data: graphData.nodes,
        links: graphData.links,
        categories: graphData.categories,
        roam: true,
        draggable: true,
        focusNodeAdjacency: true,
        edgeSymbol: ['none', 'arrow'],
        edgeSymbolSize: [0, 8],
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(0, 0, 0, 0.2)'
        },
        label: {
          show: true,
          position: 'bottom',
          formatter: function (params) {
            const name = params.data.name;
            // 长名称换行处理
            if (name.length > 20) {
              return name.substring(0, 20) + '\n' + name.substring(20);
            }
            return name;
          },
          fontSize: 10,
          color: '#2c3e50',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: 4,
          padding: [3, 6],
          borderColor: '#ddd',
          borderWidth: 1
        },
        lineStyle: {
          color: '#87CEEB',
          width: 2,
          opacity: 0.8,
          curveness: 0.1
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 4,
            opacity: 1,
            color: '#4169E1'
          },
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.4)'
          }
        },
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }
    ]
  };

  chartInstance.setOption(option);

  // 立即调用resize确保图表正确适应容器
  setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 50);

  // 监听窗口大小变化
  const resizeHandler = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };
  window.addEventListener('resize', resizeHandler);

  // 保存事件监听器引用以便清理
  chartInstance._resizeHandler = resizeHandler;

  // 设置可见性监听器
  setupVisibilityObserver();
};

// 准备拓扑图数据 - 以中心CI为核心的递归关系
const prepareAdvancedGraphData = () => {
  const nodes = [];
  const links = [];
  const nodeIds = new Set();

  // 优先使用关联分析数据中的拓扑信息，否则使用原有的topology数据
  let topology = null;
  if (relationAnalysisData.value.ci) {
    // 转换新数据结构为原有格式，并递归处理嵌套的parents和children
    const convertCiData = (ciData) => {
      if (!ciData) return null;

      return {
        res_id: ciData.resId,
        res_name: ciData.ciName,
        ci_type: ciData.ciType,
        ipv4_address: ciData.ipAddress,
        // 递归转换children
        child: ciData.children ? ciData.children.map(child => convertCiData(child)) : [],
        // 递归转换parents
        parent: ciData.parents ? ciData.parents.map(parent => convertCiData(parent)) : []
      };
    };

    topology = {
      ci: convertCiData(relationAnalysisData.value.ci),
      parent: relationAnalysisData.value.ci.parents ? relationAnalysisData.value.ci.parents.map(parent => convertCiData(parent)) : [],
      child: relationAnalysisData.value.ci.children ? relationAnalysisData.value.ci.children.map(child => convertCiData(child)) : []
    };
  } else {
    topology = reportData.value.topology;
  }

  if (!topology || !topology.ci) {
    console.warn('拓扑数据为空或缺少ci信息:', topology);
    return { nodes: [], links: [], categories: [] };
  }

  console.log('准备处理拓扑数据:', topology);

  // 设备类型配置
  const deviceTypeConfig = {
    'user_vm': { color: '#4CAF50', size: 50, icon: '🖥️', name: '虚拟机' },
    'host': { color: '#2196F3', size: 70, icon: '🏭', name: '宿主机' },
    'switch': { color: '#FF9800', size: 60, icon: '🔀', name: '交换机' },
    'sys_server': { color: '#9C27B0', size: 55, icon: '💻', name: '物理服务器' },
    'firewall': { color: '#E91E63', size: 60, icon: '🛡️', name: '防火墙' },
    'router': { color: '#3F51B5', size: 60, icon: '📡', name: '路由器' },
    'alarm_source': { color: '#F44336', size: 80, icon: '⚠️', name: '告警源' },
    // 新增关联分析数据中的设备类型
    'floating_ip': { color: '#00BCD4', size: 45, icon: '🌐', name: 'EIP' },
    'cloud_bandwidth': { color: '#795548', size: 40, icon: '📶', name: '带宽' },
    'cloud_vm_port': { color: '#8BC34A', size: 45, icon: '🔌', name: '虚机网卡' },
    'cloud_subnet': { color: '#673AB7', size: 55, icon: '🏗️', name: '子网' },
    'cloud_vpc': { color: '#3F51B5', size: 65, icon: '☁️', name: 'VPC' },
    'cloud_security_group': { color: '#E91E63', size: 50, icon: '🔒', name: '安全组' },
    'other': { color: '#607D8B', size: 50, icon: '❓', name: '其他设备' }
  };

  // 创建分类
  const categories = [
    { name: '告警源', itemStyle: { color: '#F44336' } },
    { name: '虚拟机', itemStyle: { color: '#4CAF50' } },
    { name: '宿主机', itemStyle: { color: '#2196F3' } },
    { name: '交换机', itemStyle: { color: '#FF9800' } },
    { name: '物理服务器', itemStyle: { color: '#9C27B0' } },
    { name: '防火墙', itemStyle: { color: '#E91E63' } },
    { name: '路由器', itemStyle: { color: '#3F51B5' } },
    { name: '其他设备', itemStyle: { color: '#607D8B' } }
  ];

  // 创建节点的统一函数
  const createTopologyNode = (deviceInfo, nodeType, level = 0) => {
    if (!deviceInfo) {
      console.warn('createTopologyNode: 设备信息为空');
      return null;
    }

    const nodeId = deviceInfo.res_id || deviceInfo.id || deviceInfo.uuid || deviceInfo.res_name;
    if (!nodeId) {
      console.warn('createTopologyNode: 无法获取节点ID', deviceInfo);
      return null;
    }

    if (nodeIds.has(nodeId)) {
      console.warn(`createTopologyNode: 节点ID重复 ${nodeId}, 跳过创建`);
      return null;
    }

    nodeIds.add(nodeId);

    // 判断是否为告警源
    const isAlarmSource = reportData.value.alarmInfo?.alarmSource === deviceInfo.res_name;
    // 判断是否为中心节点
    const isCenterNode = nodeType === 'center';

    // 确定设备类型
    let deviceType = 'other';
    let categoryName = '其他设备';

    if (isAlarmSource) {
      deviceType = 'alarm_source';
      categoryName = '告警源';
    } else if (deviceInfo.ci_type === 'user_vm') {
      deviceType = 'user_vm';
      categoryName = '虚拟机';
    } else if (deviceInfo.ci_type === 'host') {
      deviceType = 'host';
      categoryName = '宿主机';
    } else if (deviceInfo.ci_type === 'switch') {
      deviceType = 'switch';
      categoryName = '交换机';
    } else if (deviceInfo.ci_type === 'sys_server') {
      deviceType = 'sys_server';
      categoryName = '物理服务器';
    } else if (deviceInfo.ci_type === 'firewall') {
      deviceType = 'firewall';
      categoryName = '防火墙';
    } else if (deviceInfo.ci_type === 'router') {
      deviceType = 'router';
      categoryName = '路由器';
    }

    const config = deviceTypeConfig[deviceType] || deviceTypeConfig['other'];

    // 收集性能指标
    const metrics = {};
    if (deviceInfo.cpuIndicatorValue) metrics['CPU使用率'] = deviceInfo.cpuIndicatorValue;
    if (deviceInfo.memIndicatorValue) metrics['内存使用率'] = deviceInfo.memIndicatorValue;
    if (deviceInfo.diskIndicatorValue) metrics['磁盘使用率'] = deviceInfo.diskIndicatorValue;
    if (deviceInfo.byteInIndicatorValue) metrics['网络流入'] = deviceInfo.byteInIndicatorValue;
    if (deviceInfo.byteOutdicatorValue) metrics['网络流出'] = deviceInfo.byteOutdicatorValue;

    return {
      id: nodeId,
      name: deviceInfo.res_name || '未知设备',
      category: categoryName,
      deviceType: config.name,
      nodeType: nodeType, // center, parent, child
      level: level, // 层级深度
      symbolSize: isCenterNode ? 100 : (isAlarmSource ? 80 : config.size),
      itemStyle: {
        color: config.color,
        borderColor: isCenterNode ? '#FF6B35' : (isAlarmSource ? '#FF0000' : '#fff'),
        borderWidth: isCenterNode ? 4 : (isAlarmSource ? 3 : 2)
      },
      ip: deviceInfo.ipv4_address || null,
      status: deviceInfo.status || deviceInfo.operationg_status || deviceInfo.vm_state || null,
      manufacturer: deviceInfo.manufacturer || null,
      model: deviceInfo.product_name || deviceInfo.ci_type_alias || null,
      metrics: metrics,
      // 添加其他有用信息
      vdc_name: deviceInfo.vdc_name || null,
      vpc_name: deviceInfo.vpc_name || null,
      azone_name: deviceInfo.azone_name || null
    };
  };

  // 递归处理拓扑结构 - 同时适配两种数据结构
  const processTopologyRecursive = (topologyData, parentCiNodeId, nodeTypeInherit, level = 0) => {
    const currentNodes = [];
    const currentLinks = [];

    if (!topologyData) {
      console.warn(`processTopologyRecursive: 数据为空 (level: ${level})`);
      return { nodes: currentNodes, links: currentLinks };
    }

    // 识别数据结构类型
    const isFirstLevel = level === 0 && topologyData.ci;
    const isStandardStructure = topologyData.ci && (topologyData.parent || topologyData.child); // 标准结构：{ci:{}, parent:[], child:[]}
    const isDirectDeviceData = !topologyData.ci && (topologyData.res_id || topologyData.resId); // 直接设备数据
    const isNewStructureData = !topologyData.ci && topologyData.ciName; // 新数据结构

    let currentDeviceData = null;
    let childrenArray = [];
    let parentsArray = [];

    if (isFirstLevel || isStandardStructure) {
      // 标准结构：{ci: {}, parent: [], child: []}
      currentDeviceData = topologyData.ci;
      childrenArray = topologyData.child || [];
      parentsArray = topologyData.parent || [];
    } else if (isDirectDeviceData) {
      // 直接设备数据：设备数据包含child数组
      currentDeviceData = topologyData;
      childrenArray = topologyData.child || [];
      parentsArray = topologyData.parent || []; // 也检查parent字段
    } else if (isNewStructureData) {
      // 新数据结构：转换为标准格式
      currentDeviceData = {
        res_id: topologyData.resId,
        res_name: topologyData.ciName,
        ci_type: topologyData.ciType,
        ipv4_address: topologyData.ipAddress
      };
      childrenArray = topologyData.children || [];
      parentsArray = topologyData.parents || [];
    } else {
      return { nodes: currentNodes, links: currentLinks };
    }

    if (!currentDeviceData) {
      return { nodes: currentNodes, links: currentLinks };
    }

    // 确定节点类型
    const nodeType = level === 0 ? 'center' : nodeTypeInherit;

    // 创建当前设备节点
    const currentDeviceNode = createTopologyNode(currentDeviceData, nodeType, level);

    if (!currentDeviceNode) {
      console.warn('设备节点创建失败，跳过子项处理');
      return { nodes: currentNodes, links: currentLinks };
    }

    currentNodes.push(currentDeviceNode);

    // 如果有父级设备，创建连接（第一级除外）
    if (parentCiNodeId && currentDeviceNode.id !== parentCiNodeId) {
      currentLinks.push({
        source: parentCiNodeId,
        target: currentDeviceNode.id,
        lineStyle: {
          color: nodeTypeInherit === 'parent' ? '#67C23A' : '#409EFF',
          width: Math.max(1, 4 - level),
          type: level > 2 ? 'dashed' : 'solid'
        }
      });
    }

    // 递归处理parent数组：从当前设备连接到每个parent项
    if (parentsArray.length > 0) {
      console.log(`处理 ${parentsArray.length} 个parent节点 (level: ${level}):`, parentsArray);
      parentsArray.forEach((parentItem, index) => {
        // 传递'parent'类型给parent分支
        const result = processTopologyRecursive(parentItem, currentDeviceNode.id, 'parent', level + 1);
        currentNodes.push(...result.nodes);
        currentLinks.push(...result.links);
      });
    }

    // 递归处理child数组：从当前设备连接到每个child项
    if (childrenArray.length > 0) {
      console.log(`处理 ${childrenArray.length} 个child节点 (level: ${level}):`, childrenArray);
      childrenArray.forEach((childItem, index) => {
        // 传递'child'类型给child分支
        const result = processTopologyRecursive(childItem, currentDeviceNode.id, 'child', level + 1);
        currentNodes.push(...result.nodes);
        currentLinks.push(...result.links);
      });
    }

    return { nodes: currentNodes, links: currentLinks };
  };

  // 处理整个拓扑结构（从第一级开始）
  const result = processTopologyRecursive(topology, null, null, 0);
  nodes.push(...result.nodes);
  links.push(...result.links);

  // 找到中心节点并设置位置
  const centerNode = nodes.find(n => n.nodeType === 'center');
  if (centerNode) {
    centerNode.x = 400;
    centerNode.y = 300;
  } else {
    console.error('未找到中心节点');
    console.log('拓扑图数据处理完成(无中心节点):', {
      nodes: nodes.length,
      links: links.length,
      categories: categories.length
    });
    return { nodes: [], links: [], categories };
  }

  // 计算节点布局位置
  calculateNodePositions(nodes, centerNode);

  links.forEach((link, index) => {
    const sourceNode = nodes.find(n => n.id === link.source);
    const targetNode = nodes.find(n => n.id === link.target);
  });



  return {
    nodes,
    links,
    categories
  };

  // 计算节点位置的布局函数 - 优化多节点布局
  function calculateNodePositions(allNodes, centerNode) {

    const parentNodes = allNodes.filter(n => n.nodeType === 'parent');
    const childNodes = allNodes.filter(n => n.nodeType === 'child');


    // 按层级和类型分组
    const nodesByLevelAndType = {};
    allNodes.forEach(node => {
      if (node.nodeType !== 'center') {
        const key = `${node.level}_${node.nodeType}`;
        if (!nodesByLevelAndType[key]) nodesByLevelAndType[key] = [];
        nodesByLevelAndType[key].push(node);
      }
    });
    // 计算每组的布局
    Object.keys(nodesByLevelAndType).forEach(key => {
      const [level, nodeType] = key.split('_');
      const levelNodes = nodesByLevelAndType[key];
      const nodeCount = levelNodes.length;

      // 根据节点数量调整布局参数
      let radius, angleRange, startAngle;

      if (nodeType === 'parent') {
        // Parent节点放在上半圆区域
        radius = 180 + (parseInt(level) - 1) * 120;
        angleRange = Math.PI * 0.8; // 上半圆的80%
        startAngle = Math.PI * 0.1; // 从10%的位置开始
      } else {
        // Child节点放在下半圆区域，根据数量可能需要多层
        const baseRadius = 200 + (parseInt(level) - 1) * 120;

        if (nodeCount <= 12) {
          // 少量节点，单层圆形布局
          radius = baseRadius;
          angleRange = Math.PI * 0.8; // 下半圆的80%
          startAngle = Math.PI * 1.1; // 从下半圆开始
        } else if (nodeCount <= 24) {
          // 中等数量，双层布局
          const ring = Math.floor(levelNodes.indexOf(levelNodes[0]) / 12);
          radius = baseRadius + ring * 80;
          angleRange = Math.PI * 0.9;
          startAngle = Math.PI * 1.05;
        } else {
          // 大量节点，多层螺旋布局
          radius = baseRadius;
          angleRange = Math.PI * 2; // 完整圆形
          startAngle = 0;
        }
      }


      // 布局节点
      if (nodeCount <= 24) {
        // 标准扇形布局
        const angleStep = nodeCount > 1 ? angleRange / (nodeCount - 1) : 0;
        levelNodes.forEach((node, index) => {
          const angle = startAngle + angleStep * index;
          node.x = centerNode.x + Math.cos(angle) * radius;
          node.y = centerNode.y + Math.sin(angle) * radius;
        });
      } else {
        // 大量节点使用螺旋布局
        levelNodes.forEach((node, index) => {
          const spiralRadius = radius + (index * 15); // 螺旋半径递增
          const angle = startAngle + (index * 0.5); // 螺旋角度
          node.x = centerNode.x + Math.cos(angle) * spiralRadius;
          node.y = centerNode.y + Math.sin(angle) * spiralRadius;
        });
      }
    });
  }

  // 返回图数据
  console.log('拓扑图数据处理完成:', {
    nodes: nodes.length,
    links: links.length,
    categories: categories.length
  });
  console.log('节点详情:', nodes);
  console.log('连接详情:', links);

  return {
    nodes: nodes,
    links: links,
    categories: categories
  };
};
const disposeResult = ref('')
onMounted(() => {
  // 使用模拟数据，可以根据路由参数选择不同的数据
  // const reportType = route.query.type || '1';
  // if (reportType === '2') {
  //   // 加载report2.json数据
  //   import('./report2.json').then(report2Data => {
  //     reportData.value = report2Data.data;
  //     activeTab.value = 0; // 重置标签页
  //     nextTick(() => {
  //       initTopologyChart();
  //       initMetricTrendCharts();
  //       showDefaultMainChart();
  //     });
  //   });
  // } else {
  //   // 使用默认的report.json数据
  //   reportData.value = mockData.data;
  //   activeTab.value = 0; // 重置标签页
  //   nextTick(() => {
  //     initTopologyChart();
  //     initMetricTrendCharts();
  //     showDefaultMainChart();
  //   });
  // }

  // 后续可以使用真实接口
  loading.value = true;

  // 获取报告数据
  getReport(route.query.id, 'alarmreport.json').then(async res => {
    reportData.value = res.data;
    disposeResult.value = res.data?.disposeResult

    // 同时加载关联分析数据
    if (reportData.value.alarmInfo?.alarmSource) {
      try {
        await loadRelationAnalysisData();
      } catch (error) {
        console.warn('关联分析数据加载失败，但不影响主要功能:', error);
      }
    }

    loading.value = false;
    // 设置默认活动tab
    setDefaultActiveTab();
    nextTick(() => {
      initTopologyChart();
      showDefaultMainChart();
      initStepCharts();

      // 确保图表正确渲染
      setTimeout(() => {
        forceResizeChart();
      }, 300);

      // initMetricTrendCharts 现在由 watch(currentTabRelations) 来处理
    });
  }).catch(error => {
    loading.value = false;
    console.error('获取报告数据失败:', error);
    ElMessage.error('获取报告数据失败，请稍后重试');
  })
});

onBeforeUnmount(() => {
  // 清理ECharts实例和事件监听器
  if (chartInstance) {
    if (chartInstance._resizeHandler) {
      window.removeEventListener('resize', chartInstance._resizeHandler);
    }
    if (chartInstance._visibilityObserver) {
      chartInstance._visibilityObserver.disconnect();
    }
    chartInstance.dispose();
    chartInstance = null;
  }

  // 清理对话框图表实例
  if (dialogChartInstance) {
    dialogChartInstance.dispose();
    dialogChartInstance = null;
  }

  // 清理小型趋势图表实例
  Object.values(metricCharts.value).forEach(chart => {
    if (chart) {
      chart.dispose();
    }
  });
  metricCharts.value = {};

  // 清理主关联图表实例
  if (mainRelationChartInstance) {
    mainRelationChartInstance.dispose();
    mainRelationChartInstance = null;
  }

  // 清理步骤图表实例
  stepChartInstances.value.forEach((chart, index) => {
    if (chart && chart._resizeHandler) {
      window.removeEventListener('resize', chart._resizeHandler);
    }
    if (chart) {
      chart.dispose();
    }
  });
  stepChartInstances.value.clear();
});

// 相关诊断相关函数
const handleRouterAction = (routerData) => {
  // 导出操作 - 调用接口而不是跳转
  ElMessage.info('正在处理导出请求...');

  // 解析参数字符串为对象
  const parseParams = (paramStr) => {
    if (!paramStr) return {};
    const params = {};
    const pairs = paramStr.split('&');
    pairs.forEach(pair => {
      const [key, value] = pair.split('=');
      if (key && value !== undefined) {
        params[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
    return params;
  };

  const params = parseParams(routerData.params);

  // 根据method类型调用不同的导出接口
  if (routerData.method === 'post') {
    // POST请求导出
    proxy.download(routerData.routerUrl, params, `export_${new Date().getTime()}.xlsx`);
  } else {
    // GET请求导出
    proxy.download(routerData.routerUrl, params, `export_${new Date().getTime()}.xlsx`);
  }
};

const getColumnWidth = (field) => {
  const widthMap = {
    'deviceName': 200,
    'deviceSn': 180,
    'manufacturer': 200,
    'faultType': 120,
    'faultPart': 120,
    'location': 100,
    'solution': 200,
    'status': 100
  };
  return widthMap[field] || 150;
};

const getStatusType = (status) => {
  if (!status) return 'info';
  const statusMap = {
    'ACTIVE': 'success',
    'normal': 'success',
    'error': 'danger',
    'warning': 'warning'
  };
  return statusMap[status] || 'info';
};

// 初始化步骤图表
const initStepCharts = () => {
  console.log('开始初始化步骤图表...');
  nextTick(() => {
    setTimeout(() => {
      if (reportData.value.handleResult) {
        reportData.value.handleResult.forEach((step, index) => {
          if (step.type === 'chart') {
            // 使用更可靠的方式查找图表容器
            const chartSelector = `.step-chart[data-chart-index="${index}"]`;
            const chartElement = document.querySelector(chartSelector);

            if (chartElement) {
              // 确保容器有尺寸
              if (chartElement.offsetHeight === 0) {
                chartElement.style.height = '300px';
              }

              const chart = echarts.init(chartElement);
              const option = {
                title: {
                  text: step.description,
                  left: 'center',
                  textStyle: {
                    fontSize: 14,
                    fontWeight: 'bold'
                  }
                },
                tooltip: {
                  trigger: 'axis'
                },
                xAxis: {
                  type: 'category',
                  data: step.value.dataX
                },
                yAxis: {
                  type: 'value'
                },
                series: [{
                  data: step.value.dataY,
                  type: step.value.chartType,
                  itemStyle: {
                    color: '#409EFF'
                  }
                }]
              };
              chart.setOption(option);

              // 监听窗口大小变化
              const resizeHandler = () => chart.resize();
              window.addEventListener('resize', resizeHandler);

              // 保存引用以便清理
              chart._resizeHandler = resizeHandler;

              // 存储图表实例
              stepChartInstances.value.set(index, chart);
              console.log(`图表 ${index} 初始化成功:`, step.description);
            } else {
              console.warn(`图表容器未找到: ${chartSelector}`);
              console.log('当前DOM中的图表容器:', document.querySelectorAll('.step-chart'));
            }
          }
        });
      }
    }, 100); // 给DOM更多时间渲染
  });
};

// 按钮处理函数
const handleReturn = () => {
  // 返回上一页
  router.go(-1);
};

const handleAccept = () => {
  // 采纳处置建议的逻辑
  ElMessage.success('已采纳处置建议');
  // 这里可以调用API进行相关处理
};

const handleReject = () => {
  // 不采纳处置建议的逻辑
  ElMessage.warning('已不采纳处置建议');
  // 这里可以调用API进行相关处理
};

const handleCloseDialog = () => {
  // 关闭详细指标图表弹框的逻辑
  chartDialogVisible.value = false;
  if (dialogChartInstance) {
    dialogChartInstance.dispose();
    dialogChartInstance = null;
  }
};

// 处理脚本内容，将转义的换行符转换为实际换行符
const processScriptContent = (content) => {
  if (typeof content !== 'string') return content;
  return content.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
};

// 复制到剪贴板
const copyToClipboard = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('已复制到剪贴板');
    }).catch(() => {
      // 降级处理
      fallbackCopyToClipboard(text);
    });
  } else {
    fallbackCopyToClipboard(text);
  }
};

// 降级复制方法
const fallbackCopyToClipboard = (text) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
    document.execCommand('copy');
    ElMessage.success('已复制到剪贴板');
  } catch (err) {
    ElMessage.error('复制失败');
  }
  document.body.removeChild(textArea);
};

// 显示详细图表
const showDetailChart = async (relationData, type, index) => {
  // 判断是单个指标还是关联对象
  const isRelationData = relationData.src && relationData.tar;

  if (isRelationData) {
    // 显示关联对象的详细图表（两条线）
    dialogData.value = {
      type: type,
      metricName: `${getMetricDisplayName(relationData.src.metricName)} 关联分析`,
      data: relationData
    };
    chartDialogVisible.value = true;

    await nextTick();

    if (dialogChartInstance) {
      dialogChartInstance.dispose();
    }

    const dialogChart = document.querySelector('.dialog-chart');
    if (dialogChart) {
      dialogChartInstance = echarts.init(dialogChart);

      const xAxisData = relationData.src.metric.map((_, idx) => `时刻 ${idx + 1}`);
      const srcData = relationData.src.metric.map(item => parseFloat(item[relationData.src.metricName] || 0));
      const tarData = relationData.tar.metric.map(item => parseFloat(item[relationData.tar.metricName] || 0));

      const option = {
        title: {
          show: false,
          text: `关联指标趋势对比 (相关性: ${(relationData.point * 100).toFixed(2)}%)`,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let content = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].name}</div>`;
            params.forEach(param => {
              content += `<div style="margin-bottom: 4px; color: ${param.color};">${param.seriesName}: ${param.value}</div>`;
            });
            return content;
          }
        },
        legend: {
          data: [
            `源指标: ${getMetricDisplayName(relationData.src.metricName)}`,
            `目标指标: ${getMetricDisplayName(relationData.tar.metricName)}`
          ]
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: `源指标: ${getMetricDisplayName(relationData.src.metricName)}`,
            type: 'line',
            data: srcData,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: `目标指标: ${getMetricDisplayName(relationData.tar.metricName)}`,
            type: 'line',
            data: tarData,
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            },
            lineStyle: {
              width: 2
            }
          }
        ]
      };

      dialogChartInstance.setOption(option);
    }
  } else {
    // 原有的单个指标显示逻辑
    const metricData = relationData;
    dialogData.value = {
      type: type,
      metricName: getMetricDisplayName(metricData.metricName),
      data: metricData
    };
    chartDialogVisible.value = true;

    await nextTick();

    if (dialogChartInstance) {
      dialogChartInstance.dispose();
    }

    const dialogChart = document.querySelector('.dialog-chart');
    if (dialogChart) {
      dialogChartInstance = echarts.init(dialogChart);

      const xAxisData = metricData.metric.map((_, idx) => `时刻 ${idx + 1}`);
      const metricValue = metricData.metric.map(item => parseFloat(item[metricData.metricName] || 0));

      const option = {
        title: {
          text: `${getMetricDisplayName(metricData.metricName)} 趋势分析`,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            const point = params[0];
            const dataIndex = point.dataIndex;
            const allMetrics = metricData.metric[dataIndex];
            let content = `<div style="font-weight: bold; margin-bottom: 8px;">${point.name}</div>`;
            content += `<div style="margin-bottom: 4px;">${getMetricDisplayName(metricData.metricName)}: ${point.value}</div>`;

            // 显示其他指标
            Object.keys(allMetrics).forEach(key => {
              if (key !== metricData.metricName) {
                content += `<div style="margin-bottom: 2px; color: #666;">${getMetricDisplayName(key)}: ${allMetrics[key]}</div>`;
              }
            });

            return content;
          }
        },
        legend: {
          data: [getMetricDisplayName(metricData.metricName)]
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: getMetricDisplayName(metricData.metricName),
            type: 'line',
            stack: 'Total',
            data: metricValue,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(64, 158, 255, 0.6)'
                }, {
                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'
                }]
              }
            }
          }
        ]
      };

      dialogChartInstance.setOption(option);
    }
  }
};

// 获取指标显示名称
const getMetricDisplayName = (metricName) => {
  const metricMap = {
    'cpuIndicatorValue': 'CPU使用率',
    'memIndicatorValue': '内存使用率',
    'diskIndicatorValue': '磁盘使用率',
    'diskIoIn': '磁盘读取',
    'diskIoOut': '磁盘写入',
    'byteInIndicatorValue': '网络流入',
    'byteOutdicatorValue': '网络流出'
  };
  return metricMap[metricName] || metricName;
};

// 获取CI类型显示名称
const getCiTypeName = (ciType) => {
  const ciTypeMap = {
    'user_vm': '用户虚拟机',
    'host': '宿主机',
    'switch': '交换机',
    'sys_server': '物理服务器'
  };
  return ciTypeMap[ciType] || ciType;
};

// 获取相关性颜色
const getCorrelationColor = (point) => {
  if (point >= 0.9) return '#67C23A';
  if (point >= 0.7) return '#E6A23C';
  if (point >= 0.5) return '#F56C6C';
  return '#909399';
};

// 备注相关函数
const saveRemark = () => {
  // 这里可以调用API保存备注
  ElMessage.success('备注保存成功');
  // 实际项目中可以调用保存备注的API
  // saveRemarkApi(route.query.id, remarkContent.value);
};

const clearRemark = () => {
  remarkContent.value = '';
  ElMessage.info('备注已清空');
};

// 独立获取关联分析数据
const loadRelationAnalysisData = async () => {
  if (!reportData.value.alarmInfo?.alarmSource) {
    console.warn('没有告警源信息，跳过关联分析数据获取');
    return;
  }

  try {
    const relationRes = await getRelationAnalysis(reportData.value.alarmInfo.alarmSource, 'relationAnalysis.json');
    if (relationRes && relationRes.data) {
      relationAnalysisData.value = relationRes.data;
      console.log('关联分析数据获取成功:', relationAnalysisData.value);

      // 数据加载完成后初始化相关图表
      nextTick(() => {
        initTopologyChart();
        if (relationAnalysisData.value.coFluxList && relationAnalysisData.value.coFluxList.length > 0) {
          initMetricTrendCharts();
        }
      });
    }
  } catch (error) {
    console.error('获取关联分析数据失败:', error);
  }
};

// 设置默认活动tab
const setDefaultActiveTab = () => {
  // 按优先级设置默认活动tab
  if (disposeResult.value && disposeResult.value.trim()) {
    activeMainTab.value = 'disposeResult';
  } else if (reportData.value.handleResult && reportData.value.handleResult.length > 0) {
    activeMainTab.value = 'handleResult';
  } else if (hasTopologyData.value || (relationAnalysisData.value.coFluxList && relationAnalysisData.value.coFluxList.length > 0)) {
    activeMainTab.value = 'relationAnalysis';
  } else if (reportData.value.modelSuggest && reportData.value.modelSuggest.trim()) {
    activeMainTab.value = 'modelSuggest';
  } else if (reportData.value.alarmInfo?.remark && reportData.value.alarmInfo.remark.trim()) {
    activeMainTab.value = 'remark';
  }
};

// 强制重新调整图表大小
const forceResizeChart = () => {
  if (chartInstance) {
    // 先获取容器的实际尺寸
    const container = topologyChart.value;
    if (container) {
      const rect = container.getBoundingClientRect();
      console.log('图表容器尺寸:', rect.width, 'x', rect.height);

      // 强制设置容器尺寸
      if (rect.width === 0 || rect.height === 0) {
        container.style.width = '100%';
        container.style.height = '750px';
      }

      // 调用resize
      chartInstance.resize();
    }
  }
};

// 设置容器可见性监听器
const setupVisibilityObserver = () => {
  if (!topologyChart.value) return;

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting && chartInstance) {
        // 容器变为可见时，重新调整图表大小
        setTimeout(() => {
          forceResizeChart();
        }, 100);
      }
    });
  });

  observer.observe(topologyChart.value);

  // 保存observer引用以便清理
  if (chartInstance) {
    chartInstance._visibilityObserver = observer;
  }
};



// 监听主要tab切换，重新渲染图表
watch(activeMainTab, (newTab) => {
  if (newTab === 'relationAnalysis') {
    nextTick(() => {
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        initTopologyChart();
        if (relationAnalysisData.value.coFluxList && relationAnalysisData.value.coFluxList.length > 0) {
          initMetricTrendCharts();
        }
      }, 100);
    });
  }
});

// 删除不需要的selectRelationForChart函数，因为现在点击小图表直接弹框显示

// 初始化小型趋势图（表格中的）
const initMetricTrendCharts = () => {
  if (!relationAnalysisData.value.coFluxList) return;

  const initCharts = () => {
    // 为每个relation创建小图表
    currentTabRelations.value.forEach((relationItem, index) => {
      const chartContainer = document.querySelector(`[data-chart-id="miniChart_${index}"]`);
      if (chartContainer) {
        // 清理已存在的图表实例
        if (metricCharts.value[`mini_${index}`]) {
          metricCharts.value[`mini_${index}`].dispose();
        }

        const miniChart = echarts.init(chartContainer);
        const rawData = relationItem.rawData;
        const srcData = rawData.src.metric.map(m => parseFloat(m[rawData.src.metricName] || 0));
        const tarData = rawData.tar.metric.map(m => parseFloat(m[rawData.tar.metricName] || 0));

        const option = {
          grid: { top: 8, left: 8, right: 8, bottom: 8 },
          xAxis: {
            type: 'category',
            show: false,
            data: srcData.map((_, i) => i)
          },
          yAxis: { type: 'value', show: false },
          series: [
            {
              type: 'line',
              data: srcData,
              smooth: true,
              symbol: 'none',
              lineStyle: { width: 1.5, color: '#409EFF' },
              areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
            },
            {
              type: 'line',
              data: tarData,
              smooth: true,
              symbol: 'none',
              lineStyle: { width: 1.5, color: '#67C23A' },
              areaStyle: { color: 'rgba(103, 194, 58, 0.1)' }
            }
          ]
        };

        miniChart.setOption(option);
        metricCharts.value[`mini_${index}`] = miniChart;
      }
    });
  };

  nextTick(() => {
    // 等待表格完全渲染后再初始化图表
    setTimeout(() => {
      initCharts();
    }, 100);
  });
};

// 更新主关联图表（现在直接调用showDefaultMainChart）
const updateMainRelationChart = () => {
  showDefaultMainChart();
};

// 显示默认主图表
const showDefaultMainChart = () => {
  if (!mainRelationChartInstance || !relationAnalysisData.value.coFluxList || !relationTabs.value[activeTab.value]) return;

  // 获取当前标签页的第一个relation数据，显示src和tar两条线
  const currentRelations = currentTabRelations.value;
  if (currentRelations.length === 0) return;

  const currentRelation = currentRelations[0].rawData;
  const series = [];
  const legend = [];

  // src数据线
  const srcData = currentRelation.src.metric.map(m => parseFloat(m[currentRelation.src.metricName] || 0));
  series.push({
    name: `源设备-${currentRelation.src.metricName}`,
    type: 'line',
    data: srcData,
    smooth: true,
    lineStyle: { color: '#409EFF', width: 2 },
    symbol: 'circle',
    symbolSize: 4
  });
  legend.push(`源设备-${currentRelation.src.metricName}`);

  // tar数据线
  const tarData = currentRelation.tar.metric.map(m => parseFloat(m[currentRelation.tar.metricName] || 0));
  series.push({
    name: `目标设备-${currentRelation.tar.metricName}`,
    type: 'line',
    data: tarData,
    smooth: true,
    lineStyle: { color: '#67C23A', width: 2 },
    symbol: 'circle',
    symbolSize: 4
  });
  legend.push(`目标设备-${currentRelation.tar.metricName}`);

  // 动态生成时间轴数据，基于实际数据长度
  const generateTimeAxisData = (dataLength) => {
    if (dataLength <= 0) return [];

    const now = new Date();
    const timePoints = [];
    const intervalMinutes = Math.max(60, Math.floor(24 * 60 / dataLength)); // 至少1小时间隔

    for (let i = 0; i < dataLength; i++) {
      const time = new Date(now.getTime() - (dataLength - 1 - i) * intervalMinutes * 60 * 1000);
      const timeStr = `${time.getMonth() + 1}/${time.getDate()} ${String(time.getHours()).padStart(2, '0')}:${String(time.getMinutes()).padStart(2, '0')}`;
      timePoints.push(timeStr);
    }

    return timePoints;
  };

  const dataLength = srcData.length;
  const xAxisData = generateTimeAxisData(dataLength);

  const option = {
    title: {
      text: `关联度: ${currentRelation.point}`,
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        let content = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].name}</div>`;
        params.forEach(param => {
          content += `<div style="margin-bottom: 4px; color: ${param.color};">${param.seriesName}: ${param.value}</div>`;
        });
        return content;
      }
    },
    legend: {
      data: legend,
      bottom: 10,
      itemGap: 50
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      name: '指标值'
    },
    series: series
  };

  mainRelationChartInstance.setOption(option);
};

// 监听标签页切换
watch(activeTab, () => {
  // 清理当前的小型图表
  Object.keys(metricCharts.value).forEach(key => {
    if (key.startsWith('mini_')) {
      metricCharts.value[key].dispose();
      delete metricCharts.value[key];
    }
  });

  // 重新初始化默认主图表
  nextTick(() => {
    showDefaultMainChart();
    // 小型图表的初始化由 watch(currentTabRelations) 来处理
  });
});

// 监听关联数据变化，确保图表能正确初始化
watch(currentTabRelations, (newRelations, oldRelations) => {
  // 只有当数据确实发生变化且不为空时才重新初始化
  if (newRelations && newRelations.length > 0 && JSON.stringify(newRelations) !== JSON.stringify(oldRelations)) {
    // 清理当前的小型图表
    Object.keys(metricCharts.value).forEach(key => {
      if (key.startsWith('mini_')) {
        metricCharts.value[key].dispose();
        delete metricCharts.value[key];
      }
    });

    // 延迟初始化以确保DOM完全渲染
    nextTick(() => {
      setTimeout(() => {
        initMetricTrendCharts();
        showDefaultMainChart(); // 同时更新主图表
      }, 200);
    });
  }
}, { immediate: false, deep: true });


/** 导出按钮操作 */
function handleExport() {
  proxy.download('alarm/info/hardware/export', {
    id: reportData.value.hardwareAlarm.alarmId,
  }, `维护故障统计表_${new Date().getTime()}.xlsx`)
}
</script>

<style lang="scss" scoped>
.report-container {
  .handle-Ai-result {
    width: 100%;
    height: 100%;
    font-weight: bold;
    font-size: 16px;
    color: #FF6633;
  }

  .handle-remark {
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: #333;
  }

  // 告警基本信息样式
  .alarm-info-descriptions,
  .relation-alarm-descriptions {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        .el-descriptions__cell {
          width: 50% !important;
          vertical-align: top;

          &.el-descriptions__label {
            width: auto !important;
            min-width: 120px;
            max-width: 150px;
            white-space: nowrap;
            padding-right: 10px;
          }

          &.el-descriptions__content {
            width: calc(50% - 150px) !important;
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
            line-height: 1.5;
          }
        }

        .el-descriptions__row {
          display: flex;
          flex-wrap: wrap;

          .el-descriptions__cell {
            box-sizing: border-box;
            padding: 8px 12px;
          }
        }
      }
    }
  }

  .section-card {
    margin-bottom: 20px;
    border: 1px solid #EBEEF5;
    border-radius: 8px;

    .card-header {
      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        display: flex;
        align-items: center;

        span,
        svg {
          margin-right: 8px;
          color: #409EFF;
          font-size: 18px;
        }
      }
    }
  }

  .relation-alarm {
    .no-data {
      color: #909399;
      text-align: center;
      padding: 20px;
      margin: 0;
    }
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;

    .card-header {
      width: 100%;
    }

    .custom-steps {
      display: flex;
      width: max-content;

      .step-item {
        display: flex;
        position: relative;

        .step-circle {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #67c23a;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          margin-bottom: 8px;
          box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
        }

        .step-label {
          font-size: 14px;
          color: #67c23a;
          font-weight: bold;
          text-align: center;
          line-height: 24px;
          margin-left: 10px;
        }

        .step-line {
          width: 60px;
          height: 1px;
          background: #52c41a;
          margin: 10px 20px 0 20px;
        }
      }
    }
  }

  .dispose-results {
    .steps-detail {
      .dispose-step:first-child {
        margin-top: 0;
      }
    }

    .dispose-step {
      margin-bottom: 24px;
      border: 1px solid #E4E7ED;
      border-radius: 6px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .step-header {
        background: #F5F7FA;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #E4E7ED;

        .step-number {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #52c41a;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .step-title {
          margin: 0;
          color: #303133;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .step-content {
        padding: 16px;

        .section-label {
          display: block;
          font-weight: 600;
          color: #606266;
          margin-bottom: 8px;
          font-size: 13px;
        }

        .command-section {
          margin-bottom: 16px;

          .export-link {
            color: #409EFF;
            text-decoration: underline;
            font-weight: bold;
            display: inline-block;
            padding: 6px 12px;
            border-radius: 4px;
            transition: all 0.2s ease-in-out;

            &:hover {
              background-color: #F5F7FA;
              color: #1a73e8;
            }

            .el-icon-download {
              margin-right: 4px;
            }
          }

          .command-box {
            background: #F5F7FA;
            border: 1px solid #E4E7ED;
            border-radius: 4px;
            padding: 12px;

            code {
              color: #E6A23C;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 13px;
            }
          }
        }

        .result-section {
          .result-box {
            background: #F5F5F5;
            border: 1px solid #E4E7ED;
            border-radius: 4px;
            padding: 12px;
            max-height: 300px;
            overflow-y: auto;

            pre {
              margin: 0;
              color: #303133;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              line-height: 1.4;
              white-space: pre-wrap;
              word-wrap: break-word;
            }
          }
        }
      }
    }
  }

  .handle-results {
    .handle-step {
      margin-bottom: 24px;
      border: 1px solid #E4E7ED;
      border-radius: 6px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .step-header {
        background: #F5F7FA;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #E4E7ED;

        .step-number {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #52c41a;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .step-title {
          margin: 0;
          color: #303133;
          font-size: 14px;
          font-weight: 600;
        }
      }

      .step-content {
        padding: 16px;

        .form-content {
          .el-descriptions {
            .el-descriptions__label {
              font-weight: 600;
              color: #606266;
            }
          }
        }

        .router-content {
          .el-button {
            margin-top: 8px;
          }
        }

        .table-content {
          .el-table {
            margin-top: 8px;
          }
        }

        .chart-content {
          .step-chart {
            margin-top: 8px;
            border: 1px solid #E4E7ED;
            border-radius: 4px;
          }
        }

        .json-content {

          // json-viewer 组件样式优化
          :deep(.json-viewer) {
            background: #fafafa;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 12px;
          }
        }

        .script-content {
          background: #fafafa;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          overflow: hidden;

          .script-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;

            .script-title {
              font-weight: 500;
              color: #303133;
              font-size: 14px;
            }

            .el-button {
              padding: 4px 8px;
              font-size: 12px;

              .el-icon {
                margin-right: 4px;
              }
            }
          }

          .script-body {
            margin: 0;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #2c3e50;
            background: #ffffff;
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
          }
        }

        .default-content {
          pre {
            background: #F5F7FA;
            border: 1px solid #E4E7ED;
            border-radius: 4px;
            padding: 12px;
            margin: 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;
            overflow-x: auto;
          }
        }
      }
    }
  }

  .relation-analysis {

    // 标签页样式
    .relation-tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #E4E7ED;

      .tab-item {
        padding: 12px 24px;
        cursor: pointer;
        color: #606266;
        font-size: 14px;
        font-weight: 500;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          color: #409EFF;
        }

        &.active {
          color: #409EFF;
          border-bottom-color: #409EFF;
          background-color: rgba(64, 158, 255, 0.05);
        }
      }
    }

    // 表格容器样式
    .relation-table-container {
      margin-bottom: 24px;

      .mini-chart-container {
        width: 160px;
        height: 60px;
        cursor: pointer;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }
      }
    }

    // 大图表容器样式
    .main-chart-container {
      .chart-header {
        padding: 12px 0;
        border-bottom: 1px solid #E4E7ED;
        margin-bottom: 16px;

        h5 {
          margin: 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }
      }

      .main-relation-chart {
        width: 100%;
        height: 350px;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
        background-color: #FAFAFA;
      }
    }
  }

  .topology-container {
    width: 100%;
    height: 750px;
    min-height: 750px;
    background-color: #F8F9FA;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #E4E7ED;
    position: relative;

    .topology-chart {
      width: 100%;
      height: 100%;
      min-height: 750px;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .ai-analysis {
    .ai-response {
      display: flex;
      align-items: flex-start;

      .ai-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .ai-content {
        flex: 1;
        background: #F5F7FA;
        border-radius: 8px;
        padding: 16px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 16px;
          width: 0;
          height: 0;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
          border-right: 8px solid #F5F7FA;
        }

        p {
          margin: 0;
          color: #303133;
          line-height: 1.6;
          font-size: 14px;
        }
      }
    }
  }

  .action-buttons {
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    border-radius: 0 0 8px 8px;

    .el-button {
      margin: 0 10px;
      min-width: 80px;

      &.return-btn {

        &:hover {
          background: #7d7d7d;
          border-color: #7d7d7d;
        }
      }

      &.reject-btn:hover {
        background: #f78989;
        border-color: #f78989;
      }

      &.accept-btn:hover {
        background: #85ce61;
        border-color: #85ce61;
      }
    }
  }

  // 对话框样式
  .dialog-chart-container {
    width: 100%;
    height: 500px;

    .dialog-chart {
      width: 100%;
      height: 100%;
    }
  }

  // 主要tabs样式
  .main-tabs-wrapper {
    .el-tabs__header {
      margin-bottom: 20px;

      .el-tabs__nav-wrap {
        .el-tabs__nav-scroll {
          .el-tabs__nav {
            .el-tabs__item {
              font-size: 14px;
              font-weight: 500;
              padding: 12px 24px;
              color: #606266;

              &.is-active {
                background-color: #409EFF;
                color: white;
                border-radius: 4px 4px 0 0;
                border-color: #409EFF;
              }

              &:hover:not(.is-active) {
                color: #409EFF;
              }
            }
          }
        }
      }
    }

    .el-tabs__content {
      .el-tab-pane {
        min-height: 200px;
      }
    }
  }

  // 备注样式
  .handle-remark {
    .remark-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-start;
    }
  }



  // 相关告警信息样式
  .relation-alarm-container {
    .alarm-tabs {
      .alarm-tabs-wrapper {
        .el-tabs__header {
          margin-bottom: 16px;

          .el-tabs__nav-wrap {
            .el-tabs__nav-scroll {
              .el-tabs__nav {
                .el-tabs__item {
                  max-width: 200px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  font-size: 13px;
                  padding: 8px 16px;

                  &.is-active {
                    background-color: #6ecca8;
                    color: white;
                    border-radius: 4px 4px 0 0;
                  }
                }
              }
            }
          }
        }

        .el-tabs__content {
          .el-tab-pane {
            .alarm-content {
              max-height: 400px;
              overflow-y: auto;
              border: 1px solid #E4E7ED;
              border-radius: 6px;
              background: #FAFAFA;

              .alarm-header {
                background: linear-gradient(135deg, #6ecca8 0%, #baeeda 100%);
                color: white;
                padding: 12px 16px;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-weight: 600;
                border-radius: 6px 6px 0 0;

                .alarm-time {
                  font-size: 12px;
                  opacity: 0.9;
                }
              }

              .relation-alarm-descriptions {
                padding: 16px;
                background: white;

                .el-descriptions__body {
                  .el-descriptions__table {
                    .el-descriptions__cell {
                      padding: 8px 12px;

                      &.el-descriptions__label {
                        color: #606266;
                        font-weight: 500;
                        background-color: #f5f7fa;
                      }

                      &.el-descriptions__content {
                        color: #303133;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .relation-alarm-list {
    .alarm-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 16px;
      background: #fafafa;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .alarm-header {
        background: linear-gradient(135deg, #6ecca8 0%, #baeeda 100%);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;

        .alarm-index {
          font-size: 14px;
        }

        .alarm-time {
          font-size: 12px;
          opacity: 0.9;
        }
      }

      .relation-alarm-descriptions {
        padding: 16px;
        background: white;

        .el-descriptions__body {
          .el-descriptions__table {
            .el-descriptions__cell {
              padding: 8px 12px;

              &.el-descriptions__label {
                color: #606266;
                font-weight: 500;
                background-color: #f5f7fa;
              }

              &.el-descriptions__content {
                color: #303133;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .relation-metrics-grid {
      .metric-correlation-card {
        width: 100%;
      }
    }
  }
}
</style>
